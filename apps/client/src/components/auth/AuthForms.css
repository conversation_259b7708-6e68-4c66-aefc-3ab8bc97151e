/* Authentication Forms Styles */

.auth-form-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-header h1 {
  color: #1a202c;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.auth-header p {
  color: #718096;
  font-size: 16px;
  margin: 0;
}

.error-message {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #c53030;
  font-size: 14px;
}

.error-icon {
  font-size: 16px;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  color: #2d3748;
  font-weight: 600;
  font-size: 14px;
}

.form-input {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input-error {
  border-color: #e53e3e !important;
}

.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.password-input-container .form-input {
  width: 100%;
  padding-right: 48px; /* Make room for the toggle button */
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.password-toggle:hover {
  background-color: #f7fafc;
}

.password-requirements {
  margin-top: 8px;
  padding: 12px;
  background: #fef5e7;
  border-radius: 6px;
  border-left: 4px solid #ed8936;
}

.password-error {
  color: #c53030;
  font-size: 12px;
  margin-top: 4px;
}

.submit-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.auth-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.auth-footer p {
  color: #718096;
  font-size: 14px;
  margin: 0;
}

.link-button {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  font-size: inherit;
}

.link-button:hover {
  color: #5a67d8;
}

.demo-credentials {
  margin-top: 24px;
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.demo-credentials h3 {
  color: #2d3748;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.demo-account {
  font-size: 12px;
  color: #4a5568;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 640px) {
  .auth-form-container {
    padding: 16px;
  }

  .auth-form {
    padding: 24px;
    max-width: min(400px, 95vw);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .auth-header h1 {
    font-size: 24px;
  }
}

/* Focus styles for accessibility */
.form-input:focus,
.submit-button:focus,
.link-button:focus,
.password-toggle:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .auth-form {
    border: 2px solid #000;
  }
  
  .form-input {
    border-color: #000;
  }
  
  .submit-button {
    background: #000;
    border: 2px solid #000;
  }
}
