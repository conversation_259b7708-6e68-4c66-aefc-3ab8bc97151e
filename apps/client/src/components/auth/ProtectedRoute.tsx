import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredPermissions?: string[];
  requiredRole?: string;
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredPermissions,
  requiredRole,
  fallback = <div className="access-denied">Access Denied</div>
}) => {
  const { state, hasPermission, hasAnyPermission, hasRole } = useAuth();

  // Show loading while checking authentication
  if (state.isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!state.isAuthenticated) {
    return fallback;
  }

  // Check role requirement
  if (requiredRole && !hasRole(requiredRole)) {
    return fallback;
  }

  // Check single permission requirement
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return fallback;
  }

  // Check multiple permissions requirement (user needs ANY of them)
  if (requiredPermissions && !hasAnyPermission(requiredPermissions)) {
    return fallback;
  }

  // All checks passed, render children
  return <>{children}</>;
};

export default ProtectedRoute;
