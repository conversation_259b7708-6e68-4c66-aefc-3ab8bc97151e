import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import './AuthForms.css';

interface LoginFormProps {
  onSwitchToRegister: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSwitchToRegister }) => {
  const { state, login, clearError } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [touchedFields, setTouchedFields] = useState({
    email: false,
    password: false,
  });
  const [submitAttempted, setSubmitAttempted] = useState(false);

  useEffect(() => {
    // Clear any existing errors when component mounts
    clearError();
  }, [clearError]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Mark field as touched when user starts typing
    setTouchedFields(prev => ({
      ...prev,
      [name]: true,
    }));

    // Clear error when user starts typing
    if (state.error) {
      clearError();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitAttempted(true);

    if (!formData.email || !formData.password) {
      return;
    }

    try {
      await login(formData);
    } catch {
      // Error is handled by the auth context
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name } = e.target;
    setTouchedFields(prev => ({
      ...prev,
      [name]: true,
    }));
  };

  // Helper function to determine if field should show validation error
  const shouldShowFieldError = (fieldName: keyof typeof formData) => {
    const isEmpty = !formData[fieldName];
    const hasBeenTouched = touchedFields[fieldName];
    return isEmpty && (hasBeenTouched || submitAttempted);
  };

  return (
    <div className="auth-form-container">
      <div className="auth-form">
        <div className="auth-header">
          <h1>Welcome Back</h1>
          <p>Sign in to your AI Agency account</p>
        </div>

        {state.error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            {state.error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="form">
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              onBlur={handleBlur}
              placeholder="Enter your email"
              className={`form-input ${shouldShowFieldError('email') ? 'form-input-error' : ''}`}
              autoComplete="email"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <div className="password-input-container">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="Enter your password"
                className={`form-input ${shouldShowFieldError('password') ? 'form-input-error' : ''}`}
                autoComplete="current-password"
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
          </div>

          <button
            type="submit"
            className="submit-button"
            disabled={state.isLoading || !formData.email || !formData.password}
          >
            {state.isLoading ? (
              <span className="loading-spinner">⏳ Signing in...</span>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        <div className="auth-footer">
          <p>
            Don't have an account?{' '}
            <button
              type="button"
              className="link-button"
              onClick={onSwitchToRegister}
            >
              Sign up here
            </button>
          </p>
        </div>

        <div className="demo-credentials">
          <h3>Demo Credentials</h3>
          <div className="demo-account">
            <strong>Admin Account:</strong>
            <br />
            Email: <EMAIL>
            <br />
            Password: intouch-usr
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
