import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { type Brand, useAuth } from '../../contexts/AuthContext';

interface BrandWithStats extends Brand {
  agent_count: number;
  is_active: boolean;
  created_at: string;
  description?: string;
  industry?: string;
}

const BrandManagement: React.FC = () => {
  const { hasPermission } = useAuth();
  const [brands, setBrands] = useState<BrandWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Check if user has permission to manage brands
  const canManageBrands = hasPermission('manage_brands');

  useEffect(() => {
    if (canManageBrands) {
      fetchBrands();
    } else {
      setLoading(false);
      setError('Access denied. You need Super Admin permissions to manage brands.');
    }
  }, [canManageBrands]);

  const fetchBrands = async () => {
    if (!canManageBrands) {
      setError('Access denied. You need Super Admin permissions to manage brands.');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await axios.get('/api/brands');

      // Ensure we have the expected response structure
      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        setBrands(response.data.data);
      } else {
        console.warn('Unexpected response structure:', response.data);
        setBrands([]);
      }
    } catch (err: any) {
      let errorMessage = 'Failed to fetch brands';

      if (err.response) {
        // Server responded with error status
        if (err.response.status === 403) {
          errorMessage = 'Access denied. You need Super Admin permissions to manage brands.';
        } else if (err.response.status === 401) {
          errorMessage = 'Authentication required. Please log in again.';
        } else if (err.response.data?.error) {
          errorMessage = err.response.data.error;
        }
      } else if (err.request) {
        errorMessage = 'Network error. Please check your connection.';
      }

      setError(errorMessage);
      console.error('Error fetching brands:', err);
      setBrands([]); // Ensure brands is always an array
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBrand = () => {
    setShowCreateForm(true);
  };

  const handleBrandCreated = () => {
    setShowCreateForm(false);
    fetchBrands();
  };

  const handleToggleStatus = async (brandId: number, currentStatus: boolean) => {
    try {
      await axios.put(`/api/brands/${brandId}`, {
        is_active: !currentStatus
      });
      fetchBrands();
    } catch (err) {
      console.error('Error updating brand status:', err);
    }
  };

  if (loading) {
    return (
      <div className="management-section">
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading brands...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="management-section">
        <div className="error-state">
          <p>{error}</p>
          <button onClick={fetchBrands} className="primary-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="management-section">
      <div className="section-header">
        <div>
          <h2 className="section-title">Brand Management</h2>
          <p className="section-description">
            Create and manage brands for multi-tenant organization
          </p>
        </div>
        <button onClick={handleCreateBrand} className="primary-button">
          + Create Brand
        </button>
      </div>

      {brands.length === 0 ? (
        <div className="empty-state">
          <h3>No brands found</h3>
          <p>Create your first brand to get started with multi-tenant management.</p>
        </div>
      ) : (
        <table className="data-table">
          <thead>
            <tr>
              <th>Brand</th>
              <th>Industry</th>
              <th>Agents</th>
              <th>Status</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {brands.map((brand) => (
              <tr key={brand.id}>
                <td>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {brand.logo_url && (
                      <img 
                        src={brand.logo_url} 
                        alt={`${brand.name} logo`}
                        style={{ width: '24px', height: '24px', borderRadius: '4px' }}
                      />
                    )}
                    <div>
                      <div style={{ fontWeight: '600' }}>{brand.name}</div>
                      {brand.description && (
                        <div style={{ fontSize: '12px', color: '#718096' }}>
                          {brand.description}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
                <td>{brand.industry || '-'}</td>
                <td>{brand.agent_count}</td>
                <td>
                  <span className={`status-badge ${brand.is_active ? 'status-active' : 'status-inactive'}`}>
                    {brand.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>{new Date(brand.created_at).toLocaleDateString()}</td>
                <td>
                  <div className="action-buttons">
                    <button className="action-button edit">
                      Edit
                    </button>
                    <button 
                      className="action-button"
                      onClick={() => handleToggleStatus(brand.id, brand.is_active)}
                    >
                      {brand.is_active ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {showCreateForm && (
        <CreateBrandModal 
          onClose={() => setShowCreateForm(false)}
          onSuccess={handleBrandCreated}
        />
      )}
    </div>
  );
};

// Simple modal component for creating brands
const CreateBrandModal: React.FC<{
  onClose: () => void;
  onSuccess: () => void;
}> = ({ onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    industry: '',
    logo_url: '',
    website_url: ''
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) return;

    try {
      setLoading(true);
      await axios.post('/api/brands', formData);
      onSuccess();
    } catch (err) {
      console.error('Error creating brand:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Create New Brand</h3>
          <button onClick={onClose} className="modal-close">×</button>
        </div>
        <form onSubmit={handleSubmit} className="modal-form">
          <div className="form-group">
            <label>Brand Name *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              required
            />
          </div>
          <div className="form-group">
            <label>Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              rows={3}
            />
          </div>
          <div className="form-group">
            <label>Industry</label>
            <input
              type="text"
              value={formData.industry}
              onChange={(e) => setFormData({...formData, industry: e.target.value})}
            />
          </div>
          <div className="form-group">
            <label>Logo URL</label>
            <input
              type="url"
              value={formData.logo_url}
              onChange={(e) => setFormData({...formData, logo_url: e.target.value})}
            />
          </div>
          <div className="form-group">
            <label>Website URL</label>
            <input
              type="url"
              value={formData.website_url}
              onChange={(e) => setFormData({...formData, website_url: e.target.value})}
            />
          </div>
          <div className="modal-actions">
            <button type="button" onClick={onClose} className="secondary-button">
              Cancel
            </button>
            <button type="submit" disabled={loading} className="primary-button">
              {loading ? 'Creating...' : 'Create Brand'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BrandManagement;
