import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth, type User } from '../../contexts/AuthContext';

interface UserWithBrand extends User {
  brand_name?: string;
  brand_uuid?: string;
  role_name: string;
  role_id?: number; // Add this for form compatibility
}

const UserManagement: React.FC = () => {
  const { hasPermission } = useAuth();
  const [users, setUsers] = useState<UserWithBrand[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWithBrand | null>(null);

  const isSuperAdmin = hasPermission('manage_users');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/users');
      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        setUsers(response.data.data);
      } else {
        console.warn('Unexpected response structure:', response.data);
        setUsers([]);
      }
    } catch (err: any) {
      let errorMessage = 'Failed to fetch users';

      if (err.response) {
        if (err.response.status === 403) {
          errorMessage = 'Access denied. You need proper permissions to manage users.';
        } else if (err.response.status === 401) {
          errorMessage = 'Authentication required. Please log in again.';
        } else if (err.response.data?.error) {
          errorMessage = err.response.data.error;
        }
      } else if (err.request) {
        errorMessage = 'Network error. Please check your connection.';
      }

      setError(errorMessage);
      console.error('Error fetching users:', err);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = () => {
    setShowCreateForm(true);
  };

  const handleEditUser = (user: UserWithBrand) => {
    setSelectedUser(user);
    setShowEditForm(true);
  };

  const handleUserCreated = () => {
    setShowCreateForm(false);
    fetchUsers();
  };

  const handleUserUpdated = () => {
    setShowEditForm(false);
    setSelectedUser(null);
    fetchUsers();
  };

  const handleToggleStatus = async (userId: number, currentStatus: boolean) => {
    try {
      if (currentStatus) {
        await axios.post(`/api/users/${userId}/deactivate`);
      } else {
        await axios.post(`/api/users/${userId}/activate`);
      }
      fetchUsers();
    } catch (err) {
      console.error('Error updating user status:', err);
    }
  };

  const getRoleDisplayName = (roleName: string) => {
    if (!roleName) return 'No Role';
    
    const roleMap: { [key: string]: string } = {
      'super_admin': 'Super Admin',
      'brand_admin': 'Brand Admin',
      'editor': 'Editor',
      'operator': 'Operator'
    };
    return roleMap[roleName] || roleName;
  };

  const getRoleBadgeClass = (roleName: string) => {
    if (!roleName) return 'role-badge role-default';
    
    const classMap: { [key: string]: string } = {
      'super_admin': 'role-super-admin',
      'brand_admin': 'role-brand-admin',
      'editor': 'role-editor',
      'operator': 'role-operator'
    };
    return `role-badge ${classMap[roleName] || 'role-default'}`;
  };

  if (loading) {
    return (
      <div className="management-section">
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading users...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="management-section">
        <div className="error-state">
          <p>{error}</p>
          <button onClick={fetchUsers} className="primary-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="management-section">
      <div className="section-header">
        <div>
          <h2 className="section-title">User Management</h2>
          <p className="section-description">
            {isSuperAdmin 
              ? 'Manage all users across all brands' 
              : 'Manage users within your brand'
            }
          </p>
        </div>
        <button onClick={handleCreateUser} className="primary-button">
          + Create User
        </button>
      </div>

      {users.length === 0 ? (
        <div className="empty-state">
          <h3>No users found</h3>
          <p>Create your first user to get started.</p>
        </div>
      ) : (
        <table className="data-table">
          <thead>
            <tr>
              <th>User</th>
              <th>Email</th>
              <th>Role</th>
              {isSuperAdmin && <th>Brand</th>}
              <th>Status</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr key={user.id}>
                <td>
                  <div>
                    <div style={{ fontWeight: '600' }}>
                      {user.first_name || ''} {user.last_name || ''}
                    </div>
                    <div style={{ fontSize: '12px', color: '#718096' }}>
                      @{user.username || ''}
                    </div>
                  </div>
                </td>
                <td>{user.email || ''}</td>
                <td>
                  <span className={getRoleBadgeClass(user.role_name || '')}>
                    {getRoleDisplayName(user.role_name || '')}
                  </span>
                </td>
                {isSuperAdmin && (
                  <td>{user.brand_name || 'No Brand'}</td>
                )}
                <td>
                  <span className={`status-badge ${user.is_active ? 'status-active' : 'status-inactive'}`}>
                    {user.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>{user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}</td>
                <td>
                  <div className="action-buttons">
                    <button 
                      className="action-button edit"
                      onClick={() => handleEditUser(user)}
                    >
                      Edit
                    </button>
                    <button 
                      className="action-button"
                      onClick={() => handleToggleStatus(user.id, user.is_active)}
                    >
                      {user.is_active ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {showCreateForm && (
        <CreateUserModal 
          onClose={() => setShowCreateForm(false)}
          onSuccess={handleUserCreated}
          isSuperAdmin={isSuperAdmin}
        />
      )}

      {showEditForm && selectedUser && (
        <EditUserModal 
          user={selectedUser}
          onClose={() => {
            setShowEditForm(false);
            setSelectedUser(null);
          }}
          onSuccess={handleUserUpdated}
          isSuperAdmin={isSuperAdmin}
        />
      )}
    </div>
  );
};

// Simple modal component for creating users
const CreateUserModal: React.FC<{
  onClose: () => void;
  onSuccess: () => void;
  isSuperAdmin: boolean;
}> = ({ onClose, onSuccess, isSuperAdmin }) => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    role_id: '',
    brand_id: ''
  });
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState<any[]>([]);
  const [brands, setBrands] = useState<any[]>([]);

  useEffect(() => {
    fetchRoles();
    if (isSuperAdmin) {
      fetchBrands();
    }
  }, [isSuperAdmin]);

  const fetchRoles = async () => {
    try {
      const response = await axios.get('/api/roles');
      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        setRoles(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching roles:', err);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await axios.get('/api/brands');
      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        setBrands(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching brands:', err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.username.trim() || !formData.email.trim() || !formData.password.trim()) return;

    try {
      setLoading(true);
      await axios.post('/api/users', formData);
      onSuccess();
    } catch (err) {
      console.error('Error creating user:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Create New User</h3>
          <button onClick={onClose} className="modal-close">×</button>
        </div>
        <form onSubmit={handleSubmit} className="modal-form">
          <div className="form-row">
            <div className="form-group">
              <label>First Name *</label>
              <input
                type="text"
                value={formData.first_name}
                onChange={(e) => setFormData({...formData, first_name: e.target.value})}
                required
              />
            </div>
            <div className="form-group">
              <label>Last Name *</label>
              <input
                type="text"
                value={formData.last_name}
                onChange={(e) => setFormData({...formData, last_name: e.target.value})}
                required
              />
            </div>
          </div>
          <div className="form-group">
            <label>Username *</label>
            <input
              type="text"
              value={formData.username}
              onChange={(e) => setFormData({...formData, username: e.target.value})}
              required
            />
          </div>
          <div className="form-group">
            <label>Email *</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              required
            />
          </div>
          <div className="form-group">
            <label>Password *</label>
            <input
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
              required
            />
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Role *</label>
              <select
                value={formData.role_id}
                onChange={(e) => setFormData({...formData, role_id: e.target.value})}
                required
              >
                <option value="">Select Role</option>
                {roles.map((role) => (
                  <option key={role.id} value={role.id}>
                    {role.name.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </option>
                ))}
              </select>
            </div>
            {isSuperAdmin && (
              <div className="form-group">
                <label>Brand</label>
                <select
                  value={formData.brand_id}
                  onChange={(e) => setFormData({...formData, brand_id: e.target.value})}
                >
                  <option value="">Select Brand</option>
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.id}>
                      {brand.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
          <div className="modal-actions">
            <button type="button" onClick={onClose} className="secondary-button">
              Cancel
            </button>
            <button type="submit" disabled={loading} className="primary-button">
              {loading ? 'Creating...' : 'Create User'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// EditUserModal component for editing users
const EditUserModal: React.FC<{
  user: UserWithBrand;
  onClose: () => void;
  onSuccess: () => void;
  isSuperAdmin: boolean;
}> = ({ user, onClose, onSuccess, isSuperAdmin }) => {
  const [formData, setFormData] = useState({
    username: user.username || '',
    email: user.email || '',
    first_name: user.first_name || '',
    last_name: user.last_name || '',
    role_id: (user.role_id || user.role?.id)?.toString() || '',
    brand_id: user.brand_id?.toString() || ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [roles, setRoles] = useState<any[]>([]);
  const [brands, setBrands] = useState<any[]>([]);

  useEffect(() => {
    fetchRoles();
    if (isSuperAdmin) {
      fetchBrands();
    }
  }, [isSuperAdmin]);

  const fetchRoles = async () => {
    try {
      const response = await axios.get('/api/roles');
      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        setRoles(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching roles:', err);
      setError('Failed to load roles');
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await axios.get('/api/brands');
      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        setBrands(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching brands:', err);
      setError('Failed to load brands');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.username.trim() || !formData.email.trim()) {
      setError('Username and email are required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log('Updating user with data:', formData);
      const response = await axios.put(`/api/users/${user.id}`, formData);
      console.log('Update response:', response.data);
      
      onSuccess();
    } catch (err: any) {
      console.error('Error updating user:', err);
      setError(err.response?.data?.error || 'Failed to update user');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Edit User: {user.username}</h3>
          <button onClick={onClose} className="modal-close">×</button>
        </div>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="modal-form">
          <div className="form-row">
            <div className="form-group">
              <label>First Name *</label>
              <input
                type="text"
                value={formData.first_name}
                onChange={(e) => setFormData({...formData, first_name: e.target.value})}
                required
              />
            </div>
            <div className="form-group">
              <label>Last Name *</label>
              <input
                type="text"
                value={formData.last_name}
                onChange={(e) => setFormData({...formData, last_name: e.target.value})}
                required
              />
            </div>
          </div>
          <div className="form-group">
            <label>Username *</label>
            <input
              type="text"
              value={formData.username}
              onChange={(e) => setFormData({...formData, username: e.target.value})}
              required
            />
          </div>
          <div className="form-group">
            <label>Email *</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              required
            />
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Role *</label>
              <select
                value={formData.role_id}
                onChange={(e) => setFormData({...formData, role_id: e.target.value})}
                required
                disabled={!isSuperAdmin}
              >
                <option value="">Select Role</option>
                {roles.map((role) => (
                  <option key={role.id} value={role.id}>
                    {role.name.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </option>
                ))}
              </select>
              {!isSuperAdmin && (
                <div className="field-note">Only Super Admins can change roles</div>
              )}
            </div>
            {isSuperAdmin && (
              <div className="form-group">
                <label>Brand</label>
                <select
                  value={formData.brand_id}
                  onChange={(e) => setFormData({...formData, brand_id: e.target.value})}
                >
                  <option value="">Select Brand</option>
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.id}>
                      {brand.name}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
          <div className="modal-actions">
            <button type="button" onClick={onClose} className="secondary-button">
              Cancel
            </button>
            <button type="submit" disabled={loading} className="primary-button">
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserManagement;
