import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './UserShareModal.css';

interface User {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role_name: string;
  display_name: string;
}

interface SessionParticipant {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
  accessType: 'view' | 'edit';
  isActive: boolean;
  lastActive: Date;
}

interface UserShareModalProps {
  onClose: () => void;
  onShare: (userId: string, accessType: 'view' | 'edit') => void;
  sessionId: string;
  currentParticipants: SessionParticipant[];
}

const UserShareModal: React.FC<UserShareModalProps> = ({
  onClose,
  onShare,
  sessionId,
  currentParticipants
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [accessType, setAccessType] = useState<'view' | 'edit'>('view');
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch users from the API with search functionality
  const fetchUsers = async (search = '') => {
    try {
      setSearching(true);
      const url = search.trim()
        ? `/api/users/for-sharing?search=${encodeURIComponent(search.trim())}`
        : '/api/users/for-sharing';

      const response = await axios.get(url);

      // Filter out users who are already participants
      const participantEmails = currentParticipants.map(p => p.email);
      const availableUsers = response.data.data.filter(
        (user: User) => !participantEmails.includes(user.email)
      );

      setUsers(availableUsers);
      setError(null);
    } catch (error) {
      console.error('Failed to fetch users:', error);
      setError('Failed to load users. Please try again.');
    } finally {
      setSearching(false);
      setLoading(false);
    }
  };

  // Initial fetch on component mount
  useEffect(() => {
    setLoading(true);
    fetchUsers();
  }, [currentParticipants]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm !== '') {
        fetchUsers(searchTerm);
      } else {
        fetchUsers();
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
  };

  const handleShare = () => {
    if (!selectedUser) {
      setError('Please select a user to share with.');
      return;
    }

    onShare(selectedUser.id, accessType);
    onClose();
  };

  const handleClickOutside = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleClickOutside}>
      <div className="share-modal">
        <div className="share-modal-header">
          <h3>Share Session</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        {error && <div className="error-message">{error}</div>}
        
        <div className="share-modal-content">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search users by name, email, or role..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="user-list">
            {loading || searching ? (
              <div className="loading-message">
                {loading ? 'Loading users...' : 'Searching...'}
              </div>
            ) : users.length === 0 ? (
              <div className="no-results">
                {searchTerm.trim() ? 'No users found matching your search' : 'No users available'}
              </div>
            ) : (
              users.map((user: User) => (
                <div
                  key={user.id}
                  className={`user-item ${selectedUser?.id === user.id ? 'selected' : ''}`}
                  onClick={() => handleUserSelect(user)}
                >
                  <div className="user-avatar">
                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                  </div>
                  <div className="user-details">
                    <div className="user-name">{user.display_name || `${user.first_name} ${user.last_name}`}</div>
                    <div className="user-email">{user.email}</div>
                    <div className="user-role">{user.role_name}</div>
                  </div>
                </div>
              ))
            )}
          </div>
          
          {selectedUser && (
            <div className="access-control">
              <h4>Access Type for {selectedUser.first_name} {selectedUser.last_name}</h4>
              <div className="access-options">
                <label className="access-option">
                  <input
                    type="radio"
                    name="accessType"
                    value="view"
                    checked={accessType === 'view'}
                    onChange={() => setAccessType('view')}
                  />
                  <span className="access-label">View Only</span>
                  <span className="access-description">
                    Can view the conversation but cannot send messages
                  </span>
                </label>
                
                <label className="access-option">
                  <input
                    type="radio"
                    name="accessType"
                    value="edit"
                    checked={accessType === 'edit'}
                    onChange={() => setAccessType('edit')}
                  />
                  <span className="access-label">Full Access</span>
                  <span className="access-description">
                    Can view the conversation and send messages
                  </span>
                </label>
              </div>
            </div>
          )}
        </div>
        
        <div className="share-modal-footer">
          <button className="cancel-button" onClick={onClose}>Cancel</button>
          <button 
            className="share-button"
            onClick={handleShare}
            disabled={!selectedUser}
          >
            Share
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserShareModal;
