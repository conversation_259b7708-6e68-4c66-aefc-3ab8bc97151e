import React, { useState, useRef, useEffect } from 'react';
import ChatMessage from './ChatMessage';
import AgentService from '../../services/AgentService';
import { useAuth } from '../../contexts/AuthContext';
import UserShareModal from './UserShareModal';
import ActiveUserBadge from './ActiveUserBadge';
import { detectAndExtractJson, extractTextContent } from '../../utils/jsonDetection';
import { artifactService } from '../../services/artifactService';
import type { ChatMessage as ChatMessageType } from '../../services/AgentService';
import './ChatWindow.css';

// Define types for session participants
interface SessionParticipant {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
  accessType: 'view' | 'edit';
  isActive: boolean;
  lastActive: Date;
}

interface ChatWindowProps {
  onJsonOutput?: (jsonData: object | null) => void;
  onJsonDetected?: (hasJson: boolean) => void;
  onSessionChange?: (sessionId: string | null) => void;
}

const ChatWindow: React.FC<ChatWindowProps> = ({ onJsonOutput, onJsonDetected, onSessionChange }) => {
  const { state: authState } = useAuth();
  const [messages, setMessages] = useState<ChatMessageType[]>([
    { role: 'assistant', content: 'Hello! I\'m Beeswax, your AI assistant. How can I help you today?' }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | undefined>(undefined);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [showShareModal, setShowShareModal] = useState(false);
  const [participants, setParticipants] = useState<SessionParticipant[]>([]);

  // Notify parent when sessionId changes
  useEffect(() => {
    if (onSessionChange) {
      onSessionChange(sessionId || null);
    }
  }, [sessionId, onSessionChange]);

  // Set up heartbeat to maintain active status
  useEffect(() => {
    if (!sessionId || !authState.user) return;
    
    // Add current user to participants if not already there
    const currentUser = participants.find(p => p.email === authState.user?.email);
    if (!currentUser && authState.user) {
      setParticipants(prev => [...prev, {
        id: authState.user.id.toString(),
        email: authState.user.email,
        username: authState.user.username,
        firstName: authState.user.first_name,
        lastName: authState.user.last_name,
        role: authState.user.role.name,
        accessType: 'edit', // Owner has edit access
        isActive: true,
        lastActive: new Date()
      }]);
    }
    
    // Set up heartbeat interval
    const heartbeatInterval = setInterval(() => {
      if (sessionId && authState.user) {
        // Update user's active status
        AgentService.updateSessionActivity(sessionId);
        
        // Fetch current participants
        AgentService.getSessionParticipants(sessionId)
          .then(response => {
            setParticipants(response);
          })
          .catch(error => {
            console.error('Failed to fetch session participants:', error);
          });
      }
    }, 10000); // Every 10 seconds
    
    // Set up beforeunload event to mark user as inactive when leaving
    const handleBeforeUnload = () => {
      if (sessionId) {
        AgentService.leaveSession(sessionId);
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      clearInterval(heartbeatInterval);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // Mark user as inactive when component unmounts
      if (sessionId) {
        AgentService.leaveSession(sessionId);
      }
    };
  }, [sessionId, authState.user]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize session on component mount
  useEffect(() => {
    const initializeSession = async () => {
      try {
        setIsInitializing(true);
        // Get user email from auth context
        const userEmail = authState.user?.email;
        
        if (!userEmail) {
          console.warn('User not authenticated, using guest session');
        }
        
        // Create a new session - user identification will be handled on the server
        const session = await AgentService.createSession();
        setSessionId(session.id);
      } catch (error) {
        console.error('Failed to initialize session:', error);
        setMessages([
          { 
            role: 'assistant', 
            content: 'Sorry, I encountered an error initializing the session. Please try refreshing the page.' 
          }
        ]);
      } finally {
        setIsInitializing(false);
      }
    };

    if (import.meta.env.DEV && import.meta.env.VITE_USE_LOCAL_AGENT === 'true') {
      initializeSession();
    }
  }, [authState.user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    // Check if user has edit access
    const currentUser = participants.find(p => p.email === authState.user?.email);
    if (currentUser && currentUser.accessType === 'view') {
      alert('You only have view access to this session. You cannot send messages.');
      return;
    }

    const userMessage: ChatMessageType = { role: 'user', content: input };
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const response = await AgentService.sendMessage(input, sessionId);
      
      if (response.sessionId && !sessionId) {
        setSessionId(response.sessionId);
      }
      
      // Check if the response contains JSON
      const jsonResult = detectAndExtractJson(response.message);

      if (jsonResult.isJson && onJsonOutput && onJsonDetected) {
        // If JSON is detected, send it to the JSON output panel
        onJsonOutput(jsonResult.jsonData);
        onJsonDetected(true);

        // Store the JSON as an artifact
        try {
          await artifactService.storeJsonOutput(
            jsonResult.jsonData,
            sessionId,
            authState.user?.id
          );
          console.log('JSON artifact stored successfully');
        } catch (error) {
          console.error('Failed to store JSON artifact:', error);
        }

        // For the chat, show only the text content (if any)
        const textContent = extractTextContent(response.message);
        if (textContent.trim()) {
          const assistantMessage: ChatMessageType = {
            role: 'assistant',
            content: textContent
          };
          setMessages(prev => [...prev, assistantMessage]);
        }
      } else {
        // Regular text response
        if (onJsonDetected) {
          onJsonDetected(false);
        }

        const assistantMessage: ChatMessageType = {
          role: 'assistant',
          content: response.message
        };
        setMessages(prev => [...prev, assistantMessage]);
      }
    } catch (error) {
      console.error('Failed to create new session:', error);
      const errorMessage: ChatMessageType = {
        role: 'assistant',
        content: 'Sorry, I encountered an error processing your request. Please try again.'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewSession = async () => {
    if (isInitializing) return;

    if (messages.length > 1 && !window.confirm('Starting a new session will clear the current conversation. Continue?')) {
      return;
    }

    // If there's already a message being processed, we'll interrupt it
    setIsLoading(true);
    try {
      // Create a new session
      const session = await AgentService.createSession();
      setSessionId(session.id);
      
      // Reset participants
      setParticipants([{
        id: authState.user?.id.toString() || 'guest',
        email: authState.user?.email || '<EMAIL>',
        username: authState.user?.username || 'Guest',
        firstName: authState.user?.first_name || 'Guest',
        lastName: authState.user?.last_name || 'User',
        role: authState.user?.role?.name || 'guest',
        accessType: 'edit',
        isActive: true,
        lastActive: new Date()
      }]);
      
      // Send an initial greeting to the agent
      const response = await AgentService.sendMessage('Hello', session.id);

      // Check if the response contains JSON
      const jsonResult = detectAndExtractJson(response.message);

      if (jsonResult.isJson && onJsonOutput && onJsonDetected) {
        // If JSON is detected, send it to the JSON output panel
        onJsonOutput(jsonResult.jsonData);
        onJsonDetected(true);

        // Store the JSON as an artifact
        try {
          await artifactService.storeJsonOutput(
            jsonResult.jsonData,
            sessionId,
            authState.user?.id
          );
          console.log('JSON artifact stored successfully');
        } catch (error) {
          console.error('Failed to store JSON artifact:', error);
        }

        // For the chat, show only the text content (if any)
        const textContent = extractTextContent(response.message);
        if (textContent.trim()) {
          setMessages([
            { role: 'assistant', content: textContent }
          ]);
        } else {
          setMessages([
            { role: 'assistant', content: 'Hello! I\'m Beeswax, your AI assistant. How can I help you today?' }
          ]);
        }
      } else {
        // Regular text response
        if (onJsonDetected) {
          onJsonDetected(false);
        }

        setMessages([
          { role: 'assistant', content: response.message }
        ]);
      }
    } catch (error) {
      console.error('Failed to create new session:', error);
      setMessages([
        { 
          role: 'assistant', 
          content: 'Sorry, I encountered an error creating a new session. Please try again.' 
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSession = () => {
    if (isInitializing) return;

    if (!sessionId) {
      alert('No active session to delete.');
      return;
    }

    if (window.confirm('Are you sure you want to delete this session? This action cannot be undone.')) {
      // Notify server to delete session
      if (sessionId) {
        AgentService.deleteSession(sessionId);
      }

      setSessionId(undefined);
      setMessages([
        { role: 'assistant', content: 'Session deleted. How can I help you today?' }
      ]);
      setParticipants([]);

      // If there was a message being processed, stop the loading state
      setIsLoading(false);
    }
  };

  const handleExportSession = () => {
    if (messages.length <= 1) {
      alert('There is no conversation to export yet.');
      return;
    }
    
    // Create a JSON file with the conversation
    const conversationData = {
      sessionId: sessionId || 'unsaved-session',
      timestamp: new Date().toISOString(),
      messages: messages
    };
    
    const dataStr = JSON.stringify(conversationData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    // Create a download link and trigger it
    const exportFileDefaultName = `chat-export-${new Date().toISOString().slice(0,10)}.json`;
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const handleShareSession = () => {
    if (!sessionId) {
      alert('You need an active session to share.');
      return;
    }
    
    setShowShareModal(true);
  };

  const handleShareModalClose = () => {
    setShowShareModal(false);
  };

  const handleShareWithUser = async (userId: string, accessType: 'view' | 'edit') => {
    if (!sessionId) return;
    
    try {
      await AgentService.shareSession(sessionId, userId, accessType);
      // Refresh participants list
      const updatedParticipants = await AgentService.getSessionParticipants(sessionId);
      setParticipants(updatedParticipants);
    } catch (error) {
      console.error('Failed to share session:', error);
      alert('Failed to share session. Please try again.');
    }
  };

  // Filter to show only active participants
  const activeParticipants = participants.filter(p => p.isActive);

  return (
    <div className="chat-window">
      <div className="chat-header">
        <div className="session-info">
          {sessionId ? (
            <span className="session-id">Session ID: {sessionId.substring(0, 8)}...</span>
          ) : (
            <span className="session-status">New conversation</span>
          )}
          
          {/* Active participants display */}
          <div className="active-participants">
            {activeParticipants.map(participant => (
              <ActiveUserBadge 
                key={participant.id} 
                participant={participant} 
              />
            ))}
          </div>
        </div>
        {/* Session management controls - always available except during initialization */}
        <div className="session-actions">
          <button
            onClick={handleShareSession}
            className="session-button share-session"
            title="Share this session with other users (available anytime)"
            disabled={isInitializing || !sessionId}
          >
            Share Session
          </button>
          <button
            onClick={handleNewSession}
            className="session-button new-session"
            title="Start a new conversation (available anytime)"
            disabled={isInitializing}
          >
            New Session
          </button>
          <button
            onClick={handleDeleteSession}
            className="session-button delete-session"
            disabled={!sessionId || isInitializing}
            title="Delete current session (available anytime)"
          >
            Delete Session
          </button>
          <button
            onClick={handleExportSession}
            className="session-button export-session"
            disabled={messages.length <= 1 || isInitializing}
            title="Export conversation as JSON (available anytime)"
          >
            Export
          </button>
        </div>
      </div>
      <div className="chat-messages">
        {messages.map((message, index) => (
          <ChatMessage key={index} message={message} />
        ))}
        {(isLoading || isInitializing) && (
          <div className="loading-indicator">
            <div className="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <form className="chat-input-form" onSubmit={handleSubmit}>
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder={isLoading ? "Type your next message while I respond..." : "Type your message here..."}
          disabled={isInitializing || (participants.find(p => p.email === authState.user?.email)?.accessType === 'view')}
          className="chat-input"
        />
        <button
          type="submit"
          disabled={isLoading || isInitializing || !input.trim() || (participants.find(p => p.email === authState.user?.email)?.accessType === 'view')}
          className={`chat-submit-button ${isLoading ? 'waiting' : ''}`}
          title={isLoading ? "Please wait for the current response to complete" : "Send message"}
        >
          {isLoading ? "Wait..." : "Send"}
        </button>
      </form>
      
      {/* Share Modal */}
      {showShareModal && (
        <UserShareModal
          onClose={handleShareModalClose}
          onShare={handleShareWithUser}
          sessionId={sessionId || ''}
          currentParticipants={participants}
        />
      )}
    </div>
  );
};

export default ChatWindow;
