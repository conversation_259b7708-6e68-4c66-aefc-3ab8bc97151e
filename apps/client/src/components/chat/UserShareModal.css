.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.share-modal {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.share-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.share-modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #2d3748;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #718096;
}

.close-button:hover {
  color: #2d3748;
}

.share-modal-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.error-message {
  background-color: #fed7d7;
  color: #c53030;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.search-container {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
}

.user-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  margin-bottom: 16px;
}

.loading-message, .no-results {
  padding: 16px;
  text-align: center;
  color: #718096;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #e2e8f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-item:hover {
  background-color: #f7fafc;
}

.user-item.selected {
  background-color: #ebf8ff;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #4299e1;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-right: 12px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.user-email {
  font-size: 12px;
  color: #718096;
  margin-bottom: 2px;
}

.user-role {
  font-size: 11px;
  color: #4a5568;
  background-color: #e2e8f0;
  padding: 2px 6px;
  border-radius: 12px;
  display: inline-block;
  text-transform: capitalize;
}

.access-control {
  background-color: #f7fafc;
  padding: 16px;
  border-radius: 4px;
}

.access-control h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #2d3748;
}

.access-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.access-option {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
}

.access-option input {
  margin-top: 4px;
  margin-right: 8px;
}

.access-label {
  font-weight: 600;
  color: #2d3748;
  margin-right: 8px;
}

.access-description {
  font-size: 12px;
  color: #718096;
}

.share-modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid #e2e8f0;
  gap: 12px;
}

.cancel-button {
  padding: 8px 16px;
  background-color: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.share-button {
  padding: 8px 16px;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.share-button:hover {
  background-color: #3182ce;
}

.share-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}