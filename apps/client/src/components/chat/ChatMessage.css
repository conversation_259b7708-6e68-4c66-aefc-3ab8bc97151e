.chat-message {
  display: flex;
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease-in-out;
}

.user-message {
  justify-content: flex-end;
}

.assistant-message {
  justify-content: flex-start;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  font-size: 16px;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-message .message-content {
  background-color: #0084ff;
  color: white;
  border-bottom-right-radius: 4px;
}

.assistant-message .message-content {
  background-color: #f0f2f5;
  color: #1c1e21;
  border-bottom-left-radius: 4px;
}

.message-text {
  line-height: 1.4;
  white-space: pre-wrap;
}

/* Markdown styling */
.assistant-message .message-text {
  white-space: normal;
}

.assistant-message .message-text h1,
.assistant-message .message-text h2,
.assistant-message .message-text h3,
.assistant-message .message-text h4,
.assistant-message .message-text h5,
.assistant-message .message-text h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.assistant-message .message-text p {
  margin-bottom: 1em;
}

.assistant-message .message-text ul,
.assistant-message .message-text ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}

.assistant-message .message-text a {
  color: #0366d6;
  text-decoration: none;
}

.assistant-message .message-text a:hover {
  text-decoration: underline;
}

/* Code block styling */
.code-block-wrapper {
  margin: 1em 0;
  border-radius: 6px;
  overflow: hidden;
  background-color: #f6f8fa;
  border: 1px solid #ddd;
}

.code-block-header {
  background-color: #e1e4e8;
  padding: 0.5em 1em;
  font-family: monospace;
  font-size: 0.85em;
  color: #24292e;
  border-bottom: 1px solid #ddd;
}

.assistant-message .message-text pre {
  margin: 0;
  padding: 1em;
  overflow-x: auto;
  background-color: #f6f8fa;
  font-size: 0.85em;
}

.assistant-message .message-text code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.assistant-message .message-text :not(pre) > code {
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-size: 0.85em;
}

/* Table styling */
.assistant-message .message-text table {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
}

.assistant-message .message-text table th,
.assistant-message .message-text table td {
  border: 1px solid #ddd;
  padding: 6px 13px;
}

.assistant-message .message-text table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.assistant-message .message-text table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
