.json-output-container {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: 'Montserrat', sans-serif;
}

.json-output-placeholder {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border: 2px dashed #e2e8f0;
  font-family: 'Montserrat', sans-serif;
}

.json-output-placeholder h3 {
  color: #4a5568;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.json-output-placeholder p {
  color: #718096;
  font-size: 14px;
  margin: 0;
}

.json-output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 8px 8px 0 0;
}

.json-output-header h3 {
  color: #2d3748;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.json-output-controls {
  display: flex;
  gap: 8px;
}

.json-control-button {
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.json-control-button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.copy-button.copied {
  background-color: #d1fae5;
  border-color: #10b981;
  color: #065f46;
}

.json-output-content {
  flex: 1;
  overflow: auto;
  padding: 16px 20px;
  background-color: #fafafa;
}

.json-display {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #2d3748;
}

/* JSON syntax highlighting */
.json-string {
  color: #059669;
}

.json-number {
  color: #dc2626;
}

.json-boolean {
  color: #7c3aed;
}

.json-null {
  color: #6b7280;
  font-style: italic;
}

.json-key {
  color: #1e40af;
  font-weight: 600;
}

.json-bracket,
.json-brace {
  color: #374151;
  font-weight: bold;
}

.json-colon,
.json-comma {
  color: #6b7280;
}

.json-indent {
  color: transparent;
  user-select: none;
}

.json-object,
.json-array {
  display: block;
}

.json-object-items,
.json-array-items {
  margin-left: 0;
}

.json-object-item,
.json-array-item {
  display: block;
  margin: 2px 0;
}

/* Scrollbar styling */
.json-output-content::-webkit-scrollbar {
  width: 8px;
}

.json-output-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.json-output-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.json-output-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for copy success */
@keyframes copySuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.copy-button.copied {
  animation: copySuccess 0.3s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .json-output-header {
    padding: 12px 16px;
  }
  
  .json-output-header h3 {
    font-size: 14px;
  }
  
  .json-control-button {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 28px;
    height: 28px;
  }
  
  .json-output-content {
    padding: 12px 16px;
  }
  
  .json-display {
    font-size: 12px;
  }
}
