.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e6e6e6;
}

.session-info {
  font-size: 14px;
  color: #666;
}

.session-id {
  font-family: monospace;
  background-color: #eee;
  padding: 2px 6px;
  border-radius: 4px;
}

.session-status {
  font-style: italic;
}

.session-actions {
  display: flex;
  gap: 8px;
}

.session-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #ddd;
}

.new-session {
  background-color: #4caf50;
  color: white;
  border-color: #43a047;
}

.new-session:hover {
  background-color: #43a047;
}

.delete-session {
  background-color: #f44336;
  color: white;
  border-color: #e53935;
}

.delete-session:hover {
  background-color: #e53935;
}

.export-session {
  background-color: #2196f3;
  color: white;
  border-color: #1e88e5;
}

.export-session:hover {
  background-color: #1e88e5;
}

.session-button:disabled {
  background-color: #cccccc;
  border-color: #bbbbbb;
  color: #666666;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Ensure session buttons remain visually distinct and accessible */
.session-button:not(:disabled) {
  transition: all 0.2s ease;
  position: relative;
}

.session-button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Add a subtle pulse animation to indicate buttons are always available */
.session-button:not(:disabled)::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 6px;
  border: 1px solid transparent;
  transition: border-color 0.3s ease;
}

/* Show availability indicator on hover */
.session-button:not(:disabled):hover::after {
  border-color: rgba(255, 255, 255, 0.3);
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.chat-input-form {
  display: flex;
  padding: 16px;
  border-top: 1px solid #e6e6e6;
  background-color: #f9f9f9;
}

.chat-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.chat-input:focus {
  border-color: #0084ff;
}

/* Visual indicator when AI is responding but input is still available */
.chat-input:not(:disabled):placeholder-shown {
  background-color: #ffffff;
}

.chat-input:not(:disabled):not(:placeholder-shown) {
  background-color: #ffffff;
  border-color: #0084ff;
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.1);
}

.chat-submit-button {
  margin-left: 8px;
  padding: 0 20px;
  background-color: #0084ff;
  color: white;
  border: none;
  border-radius: 24px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-submit-button:hover {
  background-color: #0077e6;
}

.chat-submit-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Special styling for the waiting state */
.chat-submit-button.waiting {
  background-color: #ffa500 !important;
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.loading-indicator {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.loading-dots {
  display: flex;
  align-items: center;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  margin: 0 4px;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
  animation: dot-pulse 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes dot-pulse {
  0%, 80%, 100% { 
    transform: scale(0);
  } 40% { 
    transform: scale(1.0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chat-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .session-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .session-button {
    padding: 4px 8px;
    font-size: 11px;
  }
}
