import React, { useState } from 'react';
import './ActiveUserBadge.css';

interface SessionParticipant {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
  accessType: 'view' | 'edit';
  isActive: boolean;
  lastActive: Date;
}

interface ActiveUserBadgeProps {
  participant: SessionParticipant;
}

const ActiveUserBadge: React.FC<ActiveUserBadgeProps> = ({ participant }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  
  // Get role display name
  const getRoleDisplayName = (roleName: string) => {
    const roleNames: { [key: string]: string } = {
      'super_admin': 'Super Administrator',
      'brand_admin': 'Brand Administrator',
      'editor': 'Editor',
      'operator': 'Operator'
    };
    return roleNames[roleName] || roleName;
  };
  
  // Get initials for avatar
  const getInitials = () => {
    return `${participant.firstName.charAt(0)}${participant.lastName.charAt(0)}`;
  };
  
  // Get color based on role
  const getAvatarColor = () => {
    const roleColors: { [key: string]: string } = {
      'super_admin': '#F56565', // Red
      'brand_admin': '#4299E1', // Blue
      'editor': '#48BB78',     // Green
      'operator': '#ECC94B'    // Yellow
    };
    return roleColors[participant.role] || '#A0AEC0'; // Default gray
  };
  
  return (
    <div 
      className="active-user-badge"
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <div 
        className="user-avatar" 
        style={{ backgroundColor: getAvatarColor() }}
        title={`${participant.firstName} ${participant.lastName}`}
      >
        {getInitials()}
        {participant.accessType === 'view' && (
          <span className="view-only-indicator" title="View only access">👁️</span>
        )}
      </div>
      
      {showTooltip && (
        <div className="user-tooltip">
          <div className="tooltip-header">
            <span className="tooltip-name">{participant.firstName} {participant.lastName}</span>
            <span className="tooltip-access-type">
              {participant.accessType === 'edit' ? 'Full Access' : 'View Only'}
            </span>
          </div>
          <div className="tooltip-email">{participant.email}</div>
          <div className="tooltip-role">{getRoleDisplayName(participant.role)}</div>
        </div>
      )}
    </div>
  );
};

export default ActiveUserBadge;