import React, { useState } from 'react';
import './JSONOutput.css';

interface JSONOutputProps {
  jsonData: object | null;
  isVisible: boolean;
}

const JSONOutput: React.FC<JSONOutputProps> = ({ jsonData, isVisible }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);

  if (!isVisible || !jsonData) {
    return (
      <div className="json-output-placeholder">
        <h3>JSON Output</h3>
        <p>JSON responses from the AI agent will appear here</p>
      </div>
    );
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(jsonData, null, 2));
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy JSON:', err);
    }
  };

  const renderJsonValue = (value: any, key?: string, depth: number = 0): React.ReactNode => {
    const indent = '  '.repeat(depth);
    
    if (value === null) {
      return <span className="json-null">null</span>;
    }
    
    if (typeof value === 'boolean') {
      return <span className="json-boolean">{value.toString()}</span>;
    }
    
    if (typeof value === 'number') {
      return <span className="json-number">{value}</span>;
    }
    
    if (typeof value === 'string') {
      return <span className="json-string">"{value}"</span>;
    }
    
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="json-array">[]</span>;
      }
      
      return (
        <div className="json-array">
          <span className="json-bracket">[</span>
          <div className="json-array-items">
            {value.map((item, index) => (
              <div key={index} className="json-array-item">
                <span className="json-indent">{indent}  </span>
                {renderJsonValue(item, undefined, depth + 1)}
                {index < value.length - 1 && <span className="json-comma">,</span>}
              </div>
            ))}
          </div>
          <span className="json-indent">{indent}</span>
          <span className="json-bracket">]</span>
        </div>
      );
    }
    
    if (typeof value === 'object') {
      const keys = Object.keys(value);
      if (keys.length === 0) {
        return <span className="json-object">{'{}'}</span>;
      }
      
      return (
        <div className="json-object">
          <span className="json-brace">{'{'}</span>
          <div className="json-object-items">
            {keys.map((objKey, index) => (
              <div key={objKey} className="json-object-item">
                <span className="json-indent">{indent}  </span>
                <span className="json-key">"{objKey}"</span>
                <span className="json-colon">: </span>
                {renderJsonValue(value[objKey], objKey, depth + 1)}
                {index < keys.length - 1 && <span className="json-comma">,</span>}
              </div>
            ))}
          </div>
          <span className="json-indent">{indent}</span>
          <span className="json-brace">{'}'}</span>
        </div>
      );
    }
    
    return <span>{String(value)}</span>;
  };

  return (
    <div className="json-output-container">
      <div className="json-output-header">
        <h3>JSON Output</h3>
        <div className="json-output-controls">
          <button
            className="json-control-button"
            onClick={() => setIsExpanded(!isExpanded)}
            title={isExpanded ? "Collapse" : "Expand"}
          >
            {isExpanded ? "−" : "+"}
          </button>
          <button
            className={`json-control-button copy-button ${copySuccess ? 'copied' : ''}`}
            onClick={handleCopy}
            title="Copy JSON"
          >
            {copySuccess ? "✓" : "📋"}
          </button>
        </div>
      </div>
      
      {isExpanded && (
        <div className="json-output-content">
          <pre className="json-display">
            {renderJsonValue(jsonData)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default JSONOutput;
