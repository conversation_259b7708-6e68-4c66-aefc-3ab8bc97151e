import React, { useState, useEffect } from 'react';
import { artifactService, type Artifact } from '../../services/artifactService';
import './JSONOutput.css';

interface JSONOutputProps {
  sessionId?: string | null;
}

const JSONOutput: React.FC<JSONOutputProps> = ({ sessionId }) => {
  const [copySuccess, setCopySuccess] = useState(false);
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [artifactCount, setArtifactCount] = useState(0);
  const [loadingArtifacts, setLoadingArtifacts] = useState(false);

  // Load artifacts when sessionId changes
  useEffect(() => {
    if (sessionId) {
      loadArtifactCount();
      loadArtifacts();
    }
  }, [sessionId]);

  const loadArtifactCount = async () => {
    if (!sessionId) return;

    try {
      const count = await artifactService.getSessionArtifactCount(sessionId);
      setArtifactCount(count);
    } catch (error) {
      console.error('Failed to load artifact count:', error);
    }
  };

  const loadArtifacts = async () => {
    if (!sessionId || loadingArtifacts) return;

    setLoadingArtifacts(true);
    try {
      const sessionArtifacts = await artifactService.getSessionArtifacts(sessionId, {
        limit: 20,
        offset: 0
      });
      setArtifacts(sessionArtifacts);
    } catch (error) {
      console.error('Failed to load artifacts:', error);
    } finally {
      setLoadingArtifacts(false);
    }
  };



  const handleCopyArtifact = async (artifact: Artifact) => {
    try {
      await navigator.clipboard.writeText(artifact.json_content);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy artifact JSON:', err);
    }
  };

  if (!sessionId) {
    return (
      <div className="json-output-placeholder">
        <h3>Session Artifacts</h3>
        <p>No session selected. JSON artifacts from the current session will appear here.</p>
      </div>
    );
  }





  return (
    <div className="json-output-container">
      <div className="json-output-header">
        <div className="json-header-content">
          <h3>Session Artifacts</h3>
          <span className="artifact-count">
            {artifactCount} artifact{artifactCount !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      <div className="artifacts-section">
        <div className="artifacts-list">
          {loadingArtifacts ? (
            <div className="loading-message">Loading artifacts...</div>
          ) : artifacts.length > 0 ? (
            artifacts.map((artifact) => (
              <div key={artifact.uuid} className="artifact-item">
                <div className="artifact-header">
                  <span className="artifact-date">
                    {new Date(artifact.created_at).toLocaleString()}
                  </span>
                  <button
                    className="copy-button"
                    onClick={() => handleCopyArtifact(artifact)}
                    title="Copy JSON"
                  >
                    📋
                  </button>
                </div>
                <pre className="artifact-preview">
                  {artifact.json_content}
                </pre>
              </div>
            ))
          ) : (
            <div className="no-artifacts">No artifacts found for this session</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default JSONOutput;
