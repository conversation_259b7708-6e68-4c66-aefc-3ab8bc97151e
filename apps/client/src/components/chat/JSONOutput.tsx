import React, { useState, useEffect } from 'react';
import { artifactService, type Artifact } from '../../services/artifactService';
import './JSONOutput.css';

interface JSONOutputProps {
  jsonData: object | null;
  isVisible: boolean;
  sessionId?: string | null;
}

const JSONOutput: React.FC<JSONOutputProps> = ({ jsonData, isVisible, sessionId }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [artifactCount, setArtifactCount] = useState(0);
  const [showArtifacts, setShowArtifacts] = useState(false);
  const [loadingArtifacts, setLoadingArtifacts] = useState(false);

  // Load artifacts when sessionId changes
  useEffect(() => {
    if (sessionId) {
      loadArtifactCount();
    }
  }, [sessionId]);

  const loadArtifactCount = async () => {
    if (!sessionId) return;

    try {
      const count = await artifactService.getSessionArtifactCount(sessionId);
      setArtifactCount(count);
    } catch (error) {
      console.error('Failed to load artifact count:', error);
    }
  };

  const loadArtifacts = async () => {
    if (!sessionId || loadingArtifacts) return;

    setLoadingArtifacts(true);
    try {
      const sessionArtifacts = await artifactService.getSessionArtifacts(sessionId, {
        limit: 20,
        offset: 0
      });
      setArtifacts(sessionArtifacts);
    } catch (error) {
      console.error('Failed to load artifacts:', error);
    } finally {
      setLoadingArtifacts(false);
    }
  };

  const handleShowArtifacts = async () => {
    if (!showArtifacts) {
      await loadArtifacts();
    }
    setShowArtifacts(!showArtifacts);
  };

  const handleCopyArtifact = async (artifact: Artifact) => {
    try {
      await navigator.clipboard.writeText(artifact.json_content);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy artifact JSON:', err);
    }
  };

  if (!isVisible) {
    return (
      <div className="json-output-placeholder">
        <h3>JSON Output</h3>
        <p>JSON responses from the AI agent will appear here</p>
        {sessionId && artifactCount > 0 && (
          <div className="artifact-summary">
            <p>{artifactCount} stored artifact{artifactCount !== 1 ? 's' : ''} available</p>
            <button
              className="artifact-button"
              onClick={handleShowArtifacts}
              disabled={loadingArtifacts}
            >
              {loadingArtifacts ? 'Loading...' : showArtifacts ? 'Hide Artifacts' : 'View Artifacts'}
            </button>
            {showArtifacts && (
              <div className="artifacts-list">
                {artifacts.map((artifact) => (
                  <div key={artifact.uuid} className="artifact-item">
                    <div className="artifact-header">
                      <span className="artifact-date">
                        {new Date(artifact.created_at).toLocaleString()}
                      </span>
                      <button
                        className="copy-button"
                        onClick={() => handleCopyArtifact(artifact)}
                        title="Copy JSON"
                      >
                        📋
                      </button>
                    </div>
                    <pre className="artifact-preview">
                      {artifact.json_content.substring(0, 200)}
                      {artifact.json_content.length > 200 ? '...' : ''}
                    </pre>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  if (!jsonData) {
    return (
      <div className="json-output-placeholder">
        <h3>JSON Output</h3>
        <p>JSON responses from the AI agent will appear here</p>
      </div>
    );
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(jsonData, null, 2));
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy JSON:', err);
    }
  };

  const renderJsonValue = (value: any, key?: string, depth: number = 0): React.ReactNode => {
    const indent = '  '.repeat(depth);
    
    if (value === null) {
      return <span className="json-null">null</span>;
    }
    
    if (typeof value === 'boolean') {
      return <span className="json-boolean">{value.toString()}</span>;
    }
    
    if (typeof value === 'number') {
      return <span className="json-number">{value}</span>;
    }
    
    if (typeof value === 'string') {
      return <span className="json-string">"{value}"</span>;
    }
    
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="json-array">[]</span>;
      }
      
      return (
        <div className="json-array">
          <span className="json-bracket">[</span>
          <div className="json-array-items">
            {value.map((item, index) => (
              <div key={index} className="json-array-item">
                <span className="json-indent">{indent}  </span>
                {renderJsonValue(item, undefined, depth + 1)}
                {index < value.length - 1 && <span className="json-comma">,</span>}
              </div>
            ))}
          </div>
          <span className="json-indent">{indent}</span>
          <span className="json-bracket">]</span>
        </div>
      );
    }
    
    if (typeof value === 'object') {
      const keys = Object.keys(value);
      if (keys.length === 0) {
        return <span className="json-object">{'{}'}</span>;
      }
      
      return (
        <div className="json-object">
          <span className="json-brace">{'{'}</span>
          <div className="json-object-items">
            {keys.map((objKey, index) => (
              <div key={objKey} className="json-object-item">
                <span className="json-indent">{indent}  </span>
                <span className="json-key">"{objKey}"</span>
                <span className="json-colon">: </span>
                {renderJsonValue(value[objKey], objKey, depth + 1)}
                {index < keys.length - 1 && <span className="json-comma">,</span>}
              </div>
            ))}
          </div>
          <span className="json-indent">{indent}</span>
          <span className="json-brace">{'}'}</span>
        </div>
      );
    }
    
    return <span>{String(value)}</span>;
  };

  return (
    <div className="json-output-container">
      <div className="json-output-header">
        <div className="json-header-content">
          <h3>JSON Output</h3>
          {sessionId && artifactCount > 0 && (
            <span className="artifact-count">
              {artifactCount} stored
            </span>
          )}
        </div>
        <div className="json-output-controls">
          {sessionId && artifactCount > 0 && (
            <button
              className="json-control-button artifact-button"
              onClick={handleShowArtifacts}
              disabled={loadingArtifacts}
              title="View stored artifacts"
            >
              {loadingArtifacts ? "..." : showArtifacts ? "📁" : "📂"}
            </button>
          )}
          <button
            className="json-control-button"
            onClick={() => setIsExpanded(!isExpanded)}
            title={isExpanded ? "Collapse" : "Expand"}
          >
            {isExpanded ? "−" : "+"}
          </button>
          <button
            className={`json-control-button copy-button ${copySuccess ? 'copied' : ''}`}
            onClick={handleCopy}
            title="Copy JSON"
          >
            {copySuccess ? "✓" : "📋"}
          </button>
        </div>
      </div>
      
      {isExpanded && (
        <div className="json-output-content">
          <pre className="json-display">
            {renderJsonValue(jsonData)}
          </pre>
        </div>
      )}

      {showArtifacts && (
        <div className="artifacts-section">
          <div className="artifacts-header">
            <h4>Stored Artifacts ({artifactCount})</h4>
          </div>
          <div className="artifacts-list">
            {loadingArtifacts ? (
              <div className="loading-message">Loading artifacts...</div>
            ) : artifacts.length > 0 ? (
              artifacts.map((artifact) => (
                <div key={artifact.uuid} className="artifact-item">
                  <div className="artifact-header">
                    <span className="artifact-date">
                      {new Date(artifact.created_at).toLocaleString()}
                    </span>
                    <button
                      className="copy-button"
                      onClick={() => handleCopyArtifact(artifact)}
                      title="Copy JSON"
                    >
                      📋
                    </button>
                  </div>
                  <pre className="artifact-preview">
                    {artifact.json_content.substring(0, 300)}
                    {artifact.json_content.length > 300 ? '...' : ''}
                  </pre>
                </div>
              ))
            ) : (
              <div className="no-artifacts">No artifacts found for this session</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default JSONOutput;
