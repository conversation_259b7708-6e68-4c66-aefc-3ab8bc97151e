import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import AuthPage from './pages/AuthPage';
import Dashboard from './pages/Dashboard';
import UserProfile from './pages/UserProfile';
import AdminPanel from './pages/AdminPanel';
import ProtectedRoute from './components/auth/ProtectedRoute';
import './App.css';

// Main app content that uses auth context
const AppContent: React.FC = () => {
  const { state } = useAuth();

  return (
    <Routes>
      <Route
        path="/auth"
        element={
          state.isAuthenticated ? <Navigate to="/dashboard" replace /> : <AuthPage />
        }
      />
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute fallback={<Navigate to="/auth" replace />}>
            <Dashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile"
        element={
          <ProtectedRoute fallback={<Navigate to="/auth" replace />}>
            <UserProfile />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin"
        element={
          <ProtectedRoute
            requiredPermissions={['manage_brands', 'manage_brand_users']}
            fallback={<Navigate to="/dashboard" replace />}
          >
            <AdminPanel />
          </ProtectedRoute>
        }
      />
      <Route
        path="/"
        element={
          <Navigate to={state.isAuthenticated ? "/dashboard" : "/auth"} replace />
        }
      />
      <Route
        path="*"
        element={
          <Navigate to={state.isAuthenticated ? "/dashboard" : "/auth"} replace />
        }
      />
    </Routes>
  );
};

// Main App component that provides the auth context
const App: React.FC = () => {
  return (
    <Router>
      <AuthProvider>
        <div className="App">
          <AppContent />
        </div>
      </AuthProvider>
    </Router>
  );
};

export default App;