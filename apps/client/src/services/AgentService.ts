import axios from 'axios';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface SessionParticipant {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
  accessType: 'view' | 'edit';
  isActive: boolean;
  lastActive: Date;
}

class AgentService {
  // Create a new chat session
  async createSession() {
    try {
      const response = await axios.post('/api/sessions');
      return response.data.data;
    } catch (error) {
      console.error('Error creating session:', error);
      throw new Error('Failed to create a new session with the AI agent');
    }
  }

  // Send a message to the AI agent
  async sendMessage(message: string, sessionId?: string) {
    try {
      if (!sessionId) {
        // Create a new session if none exists
        const session = await this.createSession();
        sessionId = session.id;
      }

      const response = await axios.post('/api/chat', {
        message,
        sessionId
      });

      return {
        message: response.data.data.message,
        sessionId,
        events: response.data.data.events
      };
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to communicate with the AI agent');
    }
  }

  // Delete a session
  async deleteSession(sessionId: string) {
    try {
      await axios.delete(`/api/sessions/${sessionId}`);
      return true;
    } catch (error) {
      console.error('Error deleting session:', error);
      throw new Error('Failed to delete the session');
    }
  }

  // Share a session with another user
  async shareSession(sessionId: string, userId: string, accessType: 'view' | 'edit') {
    try {
      const response = await axios.post(`/api/sessions/${sessionId}/participants`, {
        userId,
        accessType
      });
      return response.data.data;
    } catch (error) {
      console.error('Error sharing session:', error);
      throw new Error('Failed to share the session with the selected user');
    }
  }

  // Get session participants
  async getSessionParticipants(sessionId: string): Promise<SessionParticipant[]> {
    try {
      const response = await axios.get(`/api/sessions/${sessionId}`);
      
      // Transform the data to match our interface
      return response.data.data.participants.map((p: any) => ({
        id: p.user_id,
        email: p.email,
        username: p.username,
        firstName: p.first_name,
        lastName: p.last_name,
        role: p.role_name,
        accessType: p.access_type,
        isActive: p.is_active === 1,
        lastActive: new Date(p.last_active)
      }));
    } catch (error) {
      console.error('Error getting session participants:', error);
      throw new Error('Failed to get session participants');
    }
  }

  // Update user activity in a session
  async updateSessionActivity(sessionId: string, isActive = true) {
    try {
      await axios.put(`/api/sessions/${sessionId}/activity`, { isActive });
      return true;
    } catch (error) {
      console.error('Error updating session activity:', error);
      // Don't throw here, just log the error
      return false;
    }
  }

  // Leave a session (mark user as inactive)
  async leaveSession(sessionId: string) {
    try {
      await this.updateSessionActivity(sessionId, false);
      return true;
    } catch (error) {
      console.error('Error leaving session:', error);
      return false;
    }
  }
}

export default new AgentService();
