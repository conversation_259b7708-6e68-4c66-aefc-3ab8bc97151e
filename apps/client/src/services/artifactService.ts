import { API_BASE_URL } from '../config/api';

export interface Artifact {
  id: number;
  uuid: string;
  session_id?: string;
  user_id?: number;
  json_content: string;
  created_at: string;
  parsed_json?: object;
}

export interface CreateArtifactRequest {
  json_content: string | object;
  session_id?: string;
  user_id?: number;
}

export interface ArtifactListResponse {
  success: boolean;
  data: Artifact[];
  count: number;
  pagination: {
    limit: number;
    offset: number;
  };
}

export interface ArtifactResponse {
  success: boolean;
  data: Artifact;
}

export interface ArtifactCountResponse {
  success: boolean;
  data: {
    count: number;
    session_id: string;
  };
}

class ArtifactService {
  private baseUrl = `${API_BASE_URL}/artifacts`;

  /**
   * Create a new artifact
   */
  async createArtifact(data: CreateArtifactRequest): Promise<Artifact> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ArtifactResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.data as any || 'Failed to create artifact');
      }

      return result.data;
    } catch (error) {
      console.error('Error creating artifact:', error);
      throw error;
    }
  }

  /**
   * Get all artifacts with optional filtering
   */
  async getArtifacts(options: {
    session_id?: string;
    user_id?: number;
    limit?: number;
    offset?: number;
    recent?: boolean;
  } = {}): Promise<ArtifactListResponse> {
    try {
      const params = new URLSearchParams();
      
      if (options.session_id) params.append('session_id', options.session_id);
      if (options.user_id) params.append('user_id', options.user_id.toString());
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.recent) params.append('recent', 'true');

      const url = `${this.baseUrl}?${params.toString()}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ArtifactListResponse = await response.json();
      
      if (!result.success) {
        throw new Error('Failed to fetch artifacts');
      }

      return result;
    } catch (error) {
      console.error('Error fetching artifacts:', error);
      throw error;
    }
  }

  /**
   * Get a specific artifact by UUID
   */
  async getArtifact(uuid: string, parseJson: boolean = false): Promise<Artifact> {
    try {
      const params = parseJson ? '?parse_json=true' : '';
      const response = await fetch(`${this.baseUrl}/${uuid}${params}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Artifact not found');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ArtifactResponse = await response.json();
      
      if (!result.success) {
        throw new Error('Failed to fetch artifact');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching artifact:', error);
      throw error;
    }
  }

  /**
   * Delete an artifact
   */
  async deleteArtifact(uuid: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${uuid}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Artifact not found');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to delete artifact');
      }
    } catch (error) {
      console.error('Error deleting artifact:', error);
      throw error;
    }
  }

  /**
   * Get count of artifacts for a session
   */
  async getSessionArtifactCount(sessionId: string): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/session/${sessionId}/count`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ArtifactCountResponse = await response.json();
      
      if (!result.success) {
        throw new Error('Failed to fetch artifact count');
      }

      return result.data.count;
    } catch (error) {
      console.error('Error fetching artifact count:', error);
      throw error;
    }
  }

  /**
   * Get artifacts for a specific session
   */
  async getSessionArtifacts(sessionId: string, options: {
    limit?: number;
    offset?: number;
  } = {}): Promise<Artifact[]> {
    try {
      const result = await this.getArtifacts({
        session_id: sessionId,
        ...options
      });
      
      return result.data;
    } catch (error) {
      console.error('Error fetching session artifacts:', error);
      throw error;
    }
  }

  /**
   * Store JSON output as an artifact
   * This is a convenience method for the chat interface
   */
  async storeJsonOutput(
    jsonData: object, 
    sessionId?: string, 
    userId?: number
  ): Promise<Artifact> {
    try {
      return await this.createArtifact({
        json_content: typeof jsonData === 'string' ? jsonData : JSON.stringify(jsonData),
        session_id: sessionId,
        user_id: userId
      });
    } catch (error) {
      console.error('Error storing JSON output:', error);
      throw error;
    }
  }
}

export const artifactService = new ArtifactService();
export default artifactService;
