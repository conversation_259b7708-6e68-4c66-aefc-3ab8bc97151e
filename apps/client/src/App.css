/* Global App Styles */

.App {
  min-height: 100vh;
  width: 100%;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Montserrat', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f7fafc url(hexagon.jpg) no-repeat center center fixed;
  background-size: cover;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Button reset */
button {
  font-family: inherit;
}

/* Link styles */
a {
  color: #667eea;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
