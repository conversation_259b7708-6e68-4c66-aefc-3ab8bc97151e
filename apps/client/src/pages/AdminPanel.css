/* Admin Panel Styles */

.admin-panel {
  min-height: 100vh;
  background: #f7fafc;
}

.admin-header {
  background: linear-gradient(90deg, #31345F 0%, #1E236A 100%);
  padding: 16px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.admin-header .brand-text h1 {
  color: #ffffff;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
}

.admin-header .brand-text p {
  color: #cbd5e0;
  margin: 0;
  font-size: 14px;
}

.admin-header .brand-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-nav {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 24px;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 0;
}

.nav-tab {
  background: none;
  border: none;
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 600;
  color: #718096;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.nav-tab:hover {
  color: #667eea;
  background: #f7fafc;
}

.nav-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f7fafc;
}

.admin-main {
  padding: 24px;
}

/* Hamburger Menu Styles for Admin Panel */
.hamburger-menu-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  position: relative;
  z-index: 1001;
}

.hamburger-menu-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.hamburger-icon {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  justify-content: space-between;
}

.hamburger-icon span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: #ffffff;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.hamburger-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.menu-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.menu-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 320px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.menu-header {
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
}

.menu-header .user-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
}

.menu-header .user-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 16px;
}

.menu-header .user-role {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.menu-items {
  flex: 1;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  border: none;
  background: none;
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #4a5568;
}

.menu-item:hover {
  background-color: #f7fafc;
}

.menu-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.dashboard-item:hover {
  background-color: #ebf8ff;
  color: #2b6cb0;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .menu-content {
    width: 100vw;
    max-width: 320px;
  }

  .hamburger-menu-button {
    padding: 12px;
  }

  .hamburger-icon {
    width: 20px;
    height: 16px;
  }
}

.admin-content {
  max-width: 1200px;
  margin: 0 auto;
}

.access-denied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.access-denied h2 {
  color: #e53e3e;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.access-denied p {
  color: #718096;
  font-size: 16px;
  margin: 0;
}

/* Management Section Styles */
.management-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  color: #1a202c;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.section-description {
  color: #718096;
  font-size: 14px;
  margin: 4px 0 0 0;
}

.primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.primary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Table Styles */
.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.data-table th {
  background: #f7fafc;
  color: #4a5568;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-table td {
  color: #2d3748;
  font-size: 14px;
}

.data-table tr:hover {
  background: #f7fafc;
}

/* Status Badge */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: fit-content;
}

.status-active {
  background: #c6f6d5;
  color: #2f855a;
}

.status-inactive {
  background: #fed7d7;
  color: #c53030;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-button {
  background: none;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.action-button.edit {
  color: #667eea;
  border-color: #667eea;
}

.action-button.edit:hover {
  background: #eef2ff;
}

.action-button.delete {
  color: #e53e3e;
  border-color: #e53e3e;
}

.action-button.delete:hover {
  background: #fef5f5;
}

/* Loading and Empty States */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  color: #718096;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #718096;
}

.empty-state h3 {
  color: #4a5568;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-header .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .admin-main {
    padding: 16px;
  }
  
  .nav-content {
    flex-direction: column;
    gap: 0;
  }
  
  .nav-tab {
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
    border-right: none;
  }
  
  .nav-tab.active {
    border-bottom-color: #e2e8f0;
    border-left: 3px solid #667eea;
    padding-left: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .data-table {
    font-size: 12px;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px 12px;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.modal-header h3 {
  color: #1a202c;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.modal-close:hover {
  background: #f7fafc;
  color: #4a5568;
}

.modal-form {
  padding: 0 24px 24px 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #2d3748;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.secondary-button {
  background: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-button:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

/* Role Badges */
.role-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: fit-content;
}

.role-super-admin {
  background: #fed7d7;
  color: #c53030;
}

.role-brand-admin {
  background: #bee3f8;
  color: #2b6cb0;
}

.role-editor {
  background: #c6f6d5;
  color: #2f855a;
}

.role-operator {
  background: #faf089;
  color: #744210;
}

.role-default {
  background: #e2e8f0;
  color: #4a5568;
}

/* Error State */
.error-state {
  text-align: center;
  padding: 40px;
  color: #e53e3e;
}

.error-state p {
  margin: 0 0 16px 0;
  font-size: 16px;
}

/* Spinner */
.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
