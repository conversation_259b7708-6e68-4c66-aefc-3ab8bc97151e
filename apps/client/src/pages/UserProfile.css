/* User Profile Styles */

.user-profile {
  min-height: 100vh;
  background: #f7fafc;
  padding: 24px;
}

.user-profile-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #4a5568;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.2s ease;
}

.back-button:hover {
  color: #2d3748;
}

.profile-section {
  margin-bottom: 24px;
}

.profile-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.edit-profile-button {
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.edit-profile-button:hover {
  background: #3182ce;
}

.change-password-button {
  background: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.change-password-button:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

@media (max-width: 768px) {
  .user-profile {
    padding: 16px;
  }
  
  .profile-actions {
    flex-direction: column;
  }
  
  .edit-profile-button,
  .change-password-button {
    width: 100%;
  }
}