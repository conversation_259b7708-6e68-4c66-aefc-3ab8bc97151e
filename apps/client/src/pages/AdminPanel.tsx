import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import BrandManagement from '../components/admin/BrandManagement';
import UserManagement from '../components/admin/UserManagement';
import './AdminPanel.css';

const AdminPanel: React.FC = () => {
  const { state, hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('brands');
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  if (!state.user) {
    return <div>Loading...</div>;
  }

  const isSuperAdmin = hasPermission('manage_brands');
  const isBrandAdmin = hasPermission('manage_brand_users');

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  // Close menu when pressing Escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMenuOpen) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isMenuOpen]);

  if (!isSuperAdmin && !isBrandAdmin) {
    return (
      <div className="admin-panel">
        <div className="access-denied">
          <h2>Access Denied</h2>
          <p>You don't have permission to access the admin panel.</p>
          <button onClick={handleBackToDashboard} className="primary-button">
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-panel">
      <header className="admin-header">
        <div className="header-content">
          <div className="header-left">
            <div className="brand-info">
              {state.user.brand?.logo_url && (
                <img 
                  src={state.user.brand.logo_url} 
                  alt={`${state.user.brand.name} logo`}
                  className="brand-logo"
                />
              )}
              <div className="brand-text">
                <h1>Admin Panel</h1>
                <p>{isSuperAdmin ? 'Super Admin' : 'Brand Admin'} - {state.user.brand?.name || 'AI Agency'}</p>
              </div>
            </div>
          </div>
          <div className="header-right">
            <button
              className="hamburger-menu-button"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <div className="hamburger-icon">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>

            {isMenuOpen && (
              <div className="hamburger-menu">
                <div className="menu-overlay" onClick={() => setIsMenuOpen(false)}></div>
                <div className="menu-content">
                  <div className="menu-header">
                    <div className="user-details">
                      <span className="user-name">
                        {state.user.first_name} {state.user.last_name}
                      </span>
                      <span className="user-role">
                        {isSuperAdmin ? 'Super Admin' : 'Brand Admin'}
                      </span>
                    </div>
                  </div>

                  <div className="menu-items">
                    <button
                      onClick={() => {
                        handleBackToDashboard();
                        setIsMenuOpen(false);
                      }}
                      className="menu-item dashboard-item"
                    >
                      <span className="menu-icon">🏠</span>
                      Back to Dashboard
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      <nav className="admin-nav">
        <div className="nav-content">
          {isSuperAdmin && (
            <button
              className={`nav-tab ${activeTab === 'brands' ? 'active' : ''}`}
              onClick={() => setActiveTab('brands')}
            >
              🏢 Brand Management
            </button>
          )}
          <button
            className={`nav-tab ${activeTab === 'users' ? 'active' : ''}`}
            onClick={() => setActiveTab('users')}
          >
            👥 User Management
          </button>
        </div>
      </nav>

      <main className="admin-main">
        <div className="admin-content">
          {activeTab === 'brands' && isSuperAdmin && <BrandManagement />}
          {activeTab === 'users' && <UserManagement />}
        </div>
      </main>
    </div>
  );
};

export default AdminPanel;
