import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import ChatWindow from '../components/chat/ChatWindow';
import JSONOutput from '../components/chat/JSONOutput';
import './Dashboard.css';

const Dashboard: React.FC = () => {
  const { state, logout, hasPermission } = useAuth();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [jsonOutput, setJsonOutput] = useState<object | null>(null);
  const [hasJsonOutput, setHasJsonOutput] = useState(false);

  const handleLogout = () => {
    logout();
  };

  // Close menu when pressing Escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMenuOpen) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isMenuOpen]);

  const getRoleDisplayName = (roleName: string) => {
    const roleNames: { [key: string]: string } = {
      'super_admin': 'Super Administrator',
      'brand_admin': 'Brand Administrator',
      'editor': 'Editor',
      'operator': 'Operator'
    };
    return roleNames[roleName] || roleName;
  };

  const getRoleBadgeClass = (roleName: string) => {
    const roleClasses: { [key: string]: string } = {
      'super_admin': 'role-badge-super-admin',
      'brand_admin': 'role-badge-brand-admin',
      'editor': 'role-badge-editor',
      'operator': 'role-badge-operator'
    };
    return `role-badge ${roleClasses[roleName] || 'role-badge-default'}`;
  };

  if (!state.user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <div className="brand-info">
              <img src="Eversana Logo.svg" alt="Eversana Logo" className="brand-logo" />
              <div className="brand-text">
                <h1>AI AGENCY</h1>
                {/* <h1>{state.user.brand?.name || 'AI Agency'} Dashboard</h1>
                <p>Welcome back, {state.user.first_name}!</p> */}
              </div>
            </div>
          </div>
          <div className="header-right">
            <button
              className="hamburger-menu-button"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <div className="hamburger-icon">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>

            {isMenuOpen && (
              <div className="hamburger-menu">
                <div className="menu-overlay" onClick={() => setIsMenuOpen(false)}></div>
                <div className="menu-content">
                  <div className="menu-header">
                    <div className="user-details">
                      <span className="user-name">
                        {state.user.first_name} {state.user.last_name}
                      </span>
                      <span className={getRoleBadgeClass(state.user.role.name)}>
                        {getRoleDisplayName(state.user.role.name)}
                      </span>
                    </div>
                  </div>

                  <div className="menu-items">
                    {(hasPermission('manage_brands') || hasPermission('manage_brand_users')) && (
                      <button
                        onClick={() => {
                          navigate('/admin');
                          setIsMenuOpen(false);
                        }}
                        className="menu-item admin-item"
                      >
                        <span className="menu-icon">⚙️</span>
                        Admin Panel
                      </button>
                    )}
                    <button
                      onClick={() => {
                        navigate('/profile');
                        setIsMenuOpen(false);
                      }}
                      className="menu-item profile-item"
                    >
                      <span className="menu-icon">👤</span>
                      Profile
                    </button>
                    <button
                      onClick={() => {
                        handleLogout();
                        setIsMenuOpen(false);
                      }}
                      className="menu-item logout-item"
                    >
                      <span className="menu-icon">🚪</span>
                      Logout
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="dashboard-main">
        <div className="split-container">
          <div className="chat-section">
            <ChatWindow
              onJsonOutput={setJsonOutput}
              onJsonDetected={setHasJsonOutput}
            />
          </div>
          <div className="json-output-section">
            <JSONOutput
              jsonData={jsonOutput}
              isVisible={hasJsonOutput}
            />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
