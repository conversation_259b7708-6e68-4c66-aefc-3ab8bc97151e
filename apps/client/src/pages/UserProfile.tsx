import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import './UserProfile.css';

const UserProfile: React.FC = () => {
  const { state } = useAuth();
  const navigate = useNavigate();
  
  const getRoleDisplayName = (roleName: string) => {
    const roleNames: { [key: string]: string } = {
      'super_admin': 'Super Administrator',
      'brand_admin': 'Brand Administrator',
      'editor': 'Editor',
      'operator': 'Operator'
    };
    return roleNames[roleName] || roleName;
  };

  const getRoleBadgeClass = (roleName: string) => {
    const roleClasses: { [key: string]: string } = {
      'super_admin': 'role-badge-super-admin',
      'brand_admin': 'role-badge-brand-admin',
      'editor': 'role-badge-editor',
      'operator': 'role-badge-operator'
    };
    return `role-badge ${roleClasses[roleName] || 'role-badge-default'}`;
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  if (!state.user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="user-profile">
      <div className="user-profile-header">
        <button onClick={handleBackToDashboard} className="back-button">
          ← Back to Dashboard
        </button>
      </div>
      
      <div className="dashboard-content">
        <div className="welcome-section">
          <h2>Welcome to Your Workspace</h2>
          <p>You are logged in as <strong>{state.user.username}</strong> with the role of <strong>{getRoleDisplayName(state.user.role.name)}</strong>.</p>
        </div>

        <div className="user-profile-card">
          <h3>Your Profile</h3>
          <div className="profile-details">
            <div className="profile-item">
              <label>Username:</label>
              <span>{state.user.username}</span>
            </div>
            <div className="profile-item">
              <label>Email:</label>
              <span>{state.user.email}</span>
            </div>
            <div className="profile-item">
              <label>Full Name:</label>
              <span>{state.user.first_name} {state.user.last_name}</span>
            </div>
            <div className="profile-item">
              <label>Role:</label>
              <span className={getRoleBadgeClass(state.user.role.name)}>
                {getRoleDisplayName(state.user.role.name)}
              </span>
            </div>
            <div className="profile-item">
              <label>Account Status:</label>
              <span className={`status-badge ${state.user.is_active ? 'status-active' : 'status-inactive'}`}>
                {state.user.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
          
          <div className="profile-actions">
            <button className="edit-profile-button">Edit Profile</button>
            <button className="change-password-button">Change Password</button>
          </div>
        </div>

        <div className="permissions-card">
          <h3>Your Permissions</h3>
          <div className="permissions-grid">
            {state.user.role.permissions.map((permission, index) => (
              <div key={index} className="permission-item">
                <span className="permission-icon">✓</span>
                <span className="permission-name">{permission.replace(/_/g, ' ').toUpperCase()}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="quick-actions">
          <h3>Quick Actions</h3>
          <div className="actions-grid">
            <div className="action-card" onClick={handleBackToDashboard}>
              <div className="action-icon">💬</div>
              <div className="action-text">
                <h4>Chat with AI</h4>
                <p>Return to dashboard to chat with our AI assistant</p>
              </div>
            </div>
            {/* Additional quick action cards can be added here */}
          </div>
        </div>

        <div className="role-info">
          <h3>Role Information</h3>
          <div className="role-description">
            <p><strong>{getRoleDisplayName(state.user.role.name)}</strong></p>
            <p>{state.user.role.description}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
