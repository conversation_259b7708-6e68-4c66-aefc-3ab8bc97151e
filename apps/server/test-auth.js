#!/usr/bin/env node

/**
 * Authentication Test Script
 * 
 * Tests the authentication endpoints and protected routes
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testAuth() {
  console.log('🔐 Testing Authentication System');
  console.log('=====================================');

  try {
    // Test 1: Login with default admin user
    console.log('\n📋 Test 1: Admin Login');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'intouch-usr'
    });

    console.log('✅ Admin login successful');
    console.log('User:', loginResponse.data.data.user.username);
    console.log('Role:', loginResponse.data.data.user.role.name);
    console.log('Permissions:', loginResponse.data.data.user.role.permissions);

    const adminToken = loginResponse.data.data.token;

    // Test 2: Access protected route with token
    console.log('\n📋 Test 2: Access Protected Route');
    const protectedResponse = await axios.get(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });

    console.log('✅ Protected route access successful');
    console.log('Current user:', protectedResponse.data.data.username);

    // Test 3: Register new user
    console.log('\n📋 Test 3: User Registration');
    const registerResponse = await axios.post(`${BASE_URL}/api/auth/register`, {
      username: `testuser-${Date.now()}`,
      email: `test-${Date.now()}@example.com`,
      password: 'password123',
      first_name: 'Test',
      last_name: 'User',
      role_name: 'operator'
    });

    console.log('✅ User registration successful');
    console.log('New user:', registerResponse.data.data.user.username);
    console.log('Role:', registerResponse.data.data.user.role.name);

    const userToken = registerResponse.data.data.token;

    // Test 4: Access users list with admin token (should work)
    console.log('\n📋 Test 4: Admin Access to Users List');
    const usersResponse = await axios.get(`${BASE_URL}/api/users`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });

    console.log('✅ Admin can access users list');
    console.log('Users count:', usersResponse.data.data.length);

    // Test 5: Try to access users list with regular user token (should fail)
    console.log('\n📋 Test 5: Regular User Access to Users List (should fail)');
    try {
      await axios.get(`${BASE_URL}/api/users`, {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
      console.log('❌ Regular user should not access users list');
    } catch (error) {
      console.log('✅ Regular user correctly denied access');
      console.log('Error:', error.response.data.error);
    }

    // Test 6: Access without token (should fail)
    console.log('\n📋 Test 6: Access Protected Route Without Token (should fail)');
    try {
      await axios.get(`${BASE_URL}/api/users`);
      console.log('❌ Should require authentication');
    } catch (error) {
      console.log('✅ Correctly requires authentication');
      console.log('Error:', error.response.data.error);
    }

    // Test 7: Invalid token (should fail)
    console.log('\n📋 Test 7: Invalid Token (should fail)');
    try {
      await axios.get(`${BASE_URL}/api/auth/me`, {
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      });
      console.log('❌ Should reject invalid token');
    } catch (error) {
      console.log('✅ Correctly rejects invalid token');
      console.log('Error:', error.response.data.error);
    }

    // Test 8: Get available roles
    console.log('\n📋 Test 8: Get Available Roles');
    const rolesResponse = await axios.get(`${BASE_URL}/api/auth/roles`);
    console.log('✅ Available roles retrieved');
    console.log('Roles:', rolesResponse.data.data.map(r => r.name));

    // Test 9: Change password
    console.log('\n📋 Test 9: Change Password');
    const changePasswordResponse = await axios.put(`${BASE_URL}/api/auth/change-password`, {
      current_password: 'password123',
      new_password: 'newpassword123'
    }, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });

    console.log('✅ Password changed successfully');

    // Test 10: Login with new password
    console.log('\n📋 Test 10: Login with New Password');
    const newLoginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: registerResponse.data.data.user.email,
      password: 'newpassword123'
    });

    console.log('✅ Login with new password successful');

    console.log('\n✅ All Authentication Tests Passed!');
    console.log('=====================================');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Check if server is running
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/health`);
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('Checking if server is running...');
  
  const isRunning = await checkServer();
  
  if (!isRunning) {
    console.log('❌ Server is not running!');
    console.log('Please start the server first with: npm run dev');
    process.exit(1);
  }
  
  console.log('✅ Server is running');
  await testAuth();
}

main().catch(console.error);
