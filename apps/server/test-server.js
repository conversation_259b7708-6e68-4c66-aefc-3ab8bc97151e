#!/usr/bin/env node

/**
 * Manual Server Test Script
 * 
 * This script tests the server endpoints manually to verify functionality.
 * Run this after starting the server with `npm run dev`
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

// Test data
const testRole = {
  name: `test-role-${Date.now()}`,
  description: 'Test role for manual testing',
  permissions: JSON.stringify(['read', 'write'])
};

const testBrand = {
  name: `Test Brand ${Date.now()}`,
  description: 'Test brand for manual testing',
  industry: 'Technology'
};

const testUser = {
  username: `testuser-${Date.now()}`,
  email: `test-${Date.now()}@example.com`,
  password_hash: 'hashedpassword123',
  first_name: 'Test',
  last_name: 'User'
};

const testAgent = {
  name: `Test Agent ${Date.now()}`,
  description: 'Test agent for manual testing',
  type: 'chatbot',
  configuration: JSON.stringify({ model: 'gpt-3.5-turbo' }),
  model_settings: JSON.stringify({ temperature: 0.7 })
};

async function testEndpoint(method, url, data = null, expectedStatus = 200) {
  try {
    console.log(`\n${method.toUpperCase()} ${url}`);
    
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
      console.log('Request data:', JSON.stringify(data, null, 2));
    }
    
    const response = await axios(config);
    
    console.log(`✅ Status: ${response.status}`);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.status === expectedStatus) {
      return response.data;
    } else {
      console.log(`❌ Expected status ${expectedStatus}, got ${response.status}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Error: ${error.response?.status || 'Network Error'}`);
    if (error.response?.data) {
      console.log('Error response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error message:', error.message);
    }
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Manual Server Tests');
  console.log('=====================================');
  
  // Test health check
  console.log('\n📋 Testing Health Check');
  await testEndpoint('GET', '/health');
  
  // Test Roles API
  console.log('\n📋 Testing Roles API');
  const roleResponse = await testEndpoint('POST', '/api/roles', testRole, 201);
  const roleId = roleResponse?.data?.id;
  
  if (roleId) {
    await testEndpoint('GET', `/api/roles/${roleId}`);
    await testEndpoint('GET', '/api/roles');
    await testEndpoint('PUT', `/api/roles/${roleId}`, { description: 'Updated description' });
  }
  
  // Test Brands API
  console.log('\n📋 Testing Brands API');
  const brandResponse = await testEndpoint('POST', '/api/brands', testBrand, 201);
  const brandId = brandResponse?.data?.id;
  
  if (brandId) {
    await testEndpoint('GET', `/api/brands/${brandId}`);
    await testEndpoint('GET', '/api/brands');
    await testEndpoint('GET', '/api/brands/industries');
    await testEndpoint('PUT', `/api/brands/${brandId}`, { description: 'Updated description' });
  }
  
  // Test Users API
  console.log('\n📋 Testing Users API');
  if (roleId) {
    testUser.role_id = roleId;
  }
  const userResponse = await testEndpoint('POST', '/api/users', testUser, 201);
  const userId = userResponse?.data?.id;
  
  if (userId) {
    await testEndpoint('GET', `/api/users/${userId}`);
    await testEndpoint('GET', '/api/users');
    await testEndpoint('PUT', `/api/users/${userId}`, { first_name: 'Updated' });
    await testEndpoint('POST', `/api/users/${userId}/deactivate`);
    await testEndpoint('POST', `/api/users/${userId}/activate`);
  }
  
  // Test Agents API
  console.log('\n📋 Testing Agents API');
  if (brandId) {
    testAgent.brand_id = brandId;
  }
  const agentResponse = await testEndpoint('POST', '/api/agents', testAgent, 201);
  const agentId = agentResponse?.data?.id;
  
  if (agentId) {
    await testEndpoint('GET', `/api/agents/${agentId}`);
    await testEndpoint('GET', '/api/agents');
    await testEndpoint('GET', '/api/agents/types');
    await testEndpoint('PUT', `/api/agents/${agentId}`, { description: 'Updated description' });
  }
  
  // Test error handling
  console.log('\n📋 Testing Error Handling');
  await testEndpoint('GET', '/api/users/99999', null, 404);
  await testEndpoint('POST', '/api/users', {}, null, 400);
  await testEndpoint('GET', '/non-existent-route', null, 404);
  
  // Test pagination
  console.log('\n📋 Testing Pagination');
  await testEndpoint('GET', '/api/users?page=1&limit=5');
  await testEndpoint('GET', '/api/brands?page=1&limit=3');
  
  // Cleanup (optional - delete created resources)
  console.log('\n📋 Cleanup (Optional)');
  if (agentId) {
    await testEndpoint('DELETE', `/api/agents/${agentId}`);
  }
  if (userId) {
    await testEndpoint('DELETE', `/api/users/${userId}`);
  }
  if (brandId) {
    await testEndpoint('DELETE', `/api/brands/${brandId}`);
  }
  if (roleId) {
    await testEndpoint('DELETE', `/api/roles/${roleId}`);
  }
  
  console.log('\n✅ Manual Server Tests Completed');
  console.log('=====================================');
}

// Check if server is running
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/health`);
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('Checking if server is running...');
  
  const isRunning = await checkServer();
  
  if (!isRunning) {
    console.log('❌ Server is not running!');
    console.log('Please start the server first with: npm run dev');
    process.exit(1);
  }
  
  console.log('✅ Server is running');
  await runTests();
}

// Install axios if not present
try {
  require('axios');
} catch (error) {
  console.log('Installing axios for testing...');
  require('child_process').execSync('npm install axios', { stdio: 'inherit' });
}

main().catch(console.error);
