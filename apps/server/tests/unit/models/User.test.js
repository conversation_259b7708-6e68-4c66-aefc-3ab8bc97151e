const User = require('../../../src/models/User');
const Role = require('../../../src/models/Role');
const {
  createTestUser,
  createTestRole,
  expectValidUUID,
  expectValidTimestamp
} = require('../../helpers');

describe('User Model', () => {
  let testRole;

  beforeEach(async () => {
    testRole = await Role.create(createTestRole());
  });

  describe('create', () => {
    it('should create a user with valid data', async () => {
      const userData = createTestUser({ role_id: testRole.id });
      const user = await User.create(userData);

      expect(user.id).toBeDefined();
      expectValidUUID(user.uuid);
      expect(user.username).toBe(userData.username);
      expect(user.email).toBe(userData.email);
      expect(user.first_name).toBe(userData.first_name);
      expect(user.last_name).toBe(userData.last_name);
      expect(user.role_id).toBe(testRole.id);
      expect(user.is_active).toBe(1);
      expectValidTimestamp(user.created_at);
      expectValidTimestamp(user.updated_at);
    });

    it('should create user with minimal required data', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password_hash: 'hashedpassword'
      };
      const user = await User.create(userData);

      expect(user.id).toBeDefined();
      expect(user.username).toBe(userData.username);
      expect(user.email).toBe(userData.email);
      expect(user.role_id).toBeNull();
    });
  });

  describe('findByEmail', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await User.create(createTestUser({ role_id: testRole.id }));
    });

    it('should find user by email', async () => {
      const user = await User.findByEmail(testUser.email);

      expect(user).toBeDefined();
      expect(user.id).toBe(testUser.id);
      expect(user.email).toBe(testUser.email);
    });

    it('should return undefined for non-existent email', async () => {
      const user = await User.findByEmail('<EMAIL>');
      expect(user).toBeUndefined();
    });
  });

  describe('findByUsername', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await User.create(createTestUser({ role_id: testRole.id }));
    });

    it('should find user by username', async () => {
      const user = await User.findByUsername(testUser.username);

      expect(user).toBeDefined();
      expect(user.id).toBe(testUser.id);
      expect(user.username).toBe(testUser.username);
    });

    it('should return undefined for non-existent username', async () => {
      const user = await User.findByUsername('nonexistentuser');
      expect(user).toBeUndefined();
    });
  });

  describe('findWithRole', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await User.create(createTestUser({ role_id: testRole.id }));
    });

    it('should find user with role information', async () => {
      const user = await User.findWithRole(testUser.id);

      expect(user).toBeDefined();
      expect(user.id).toBe(testUser.id);
      expect(user.role_name).toBe(testRole.name);
      expect(user.role_description).toBe(testRole.description);
      expect(user.permissions).toBe(testRole.permissions);
    });

    it('should return user with null role info if no role assigned', async () => {
      const userWithoutRole = await User.create(createTestUser());
      const user = await User.findWithRole(userWithoutRole.id);

      expect(user).toBeDefined();
      expect(user.role_name).toBeNull();
    });
  });

  describe('findAllWithRoles', () => {
    beforeEach(async () => {
      // Create multiple users
      for (let i = 0; i < 3; i++) {
        await User.create(createTestUser({ role_id: testRole.id }));
      }
    });

    it('should find all users with role information', async () => {
      const users = await User.findAllWithRoles();

      expect(users).toBeInstanceOf(Array);
      expect(users.length).toBe(3);
      users.forEach(user => {
        expect(user.role_name).toBe(testRole.name);
      });
    });

    it('should filter users by active status', async () => {
      const users = await User.findAllWithRoles({ where: { is_active: 1 } });

      expect(users).toBeInstanceOf(Array);
      users.forEach(user => {
        expect(user.is_active).toBe(1);
      });
    });

    it('should limit results', async () => {
      const users = await User.findAllWithRoles({ limit: 2 });

      expect(users).toBeInstanceOf(Array);
      expect(users.length).toBe(2);
    });
  });

  describe('update', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await User.create(createTestUser({ role_id: testRole.id }));
    });

    it('should update user data', async () => {
      const updateData = {
        first_name: 'Updated',
        last_name: 'Name'
      };

      const updatedUser = await User.update(testUser.id, updateData);

      expect(updatedUser.first_name).toBe(updateData.first_name);
      expect(updatedUser.last_name).toBe(updateData.last_name);
      expect(new Date(updatedUser.updated_at).getTime()).toBeGreaterThan(
        new Date(testUser.updated_at).getTime()
      );
    });

    it('should throw error for non-existent user', async () => {
      await expect(User.update(99999, { first_name: 'Updated' }))
        .rejects.toThrow('not found');
    });
  });

  describe('deactivate', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await User.create(createTestUser({ role_id: testRole.id }));
    });

    it('should deactivate user', async () => {
      const deactivatedUser = await User.deactivate(testUser.id);

      expect(deactivatedUser.is_active).toBe(0);
      expect(new Date(deactivatedUser.updated_at).getTime()).toBeGreaterThan(
        new Date(testUser.updated_at).getTime()
      );
    });
  });

  describe('activate', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await User.create(createTestUser({ role_id: testRole.id }));
      await User.deactivate(testUser.id);
    });

    it('should activate user', async () => {
      const activatedUser = await User.activate(testUser.id);

      expect(activatedUser.is_active).toBe(1);
    });
  });

  describe('delete', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await User.create(createTestUser({ role_id: testRole.id }));
    });

    it('should delete user', async () => {
      const result = await User.delete(testUser.id);

      expect(result.success).toBe(true);
      expect(result.deletedId).toBe(testUser.id);

      // Verify user is deleted
      const deletedUser = await User.findById(testUser.id);
      expect(deletedUser).toBeUndefined();
    });

    it('should throw error for non-existent user', async () => {
      await expect(User.delete(99999))
        .rejects.toThrow('not found');
    });
  });

  describe('count', () => {
    beforeEach(async () => {
      // Create test users
      for (let i = 0; i < 3; i++) {
        await User.create(createTestUser({ role_id: testRole.id }));
      }
    });

    it('should count all users', async () => {
      const count = await User.count();
      expect(count).toBe(3);
    });

    it('should count users with filter', async () => {
      const count = await User.count({ is_active: 1 });
      expect(count).toBe(3);
    });
  });
});
