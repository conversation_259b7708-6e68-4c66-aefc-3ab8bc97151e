# Test Suite Documentation

This directory contains comprehensive test cases for the Express + Node.js backend server with SQLite database.

## Test Structure

```
tests/
├── integration/          # API endpoint integration tests
│   ├── users.test.js     # Users API tests
│   ├── brands.test.js    # Brands API tests
│   ├── roles.test.js     # Roles API tests
│   └── agents.test.js    # Agents API tests
├── unit/                 # Unit tests for models and utilities
│   └── models/
│       └── User.test.js  # User model tests
├── helpers.js            # Test helper functions and factories
├── setup.js              # Test environment setup
├── server.test.js        # Server integration tests
└── README.md            # This file
```

## Test Categories

### 1. Integration Tests
Tests the complete API endpoints including:
- **CRUD Operations**: Create, Read, Update, Delete for all entities
- **Validation**: Input validation and error handling
- **Pagination**: List endpoints with pagination
- **Filtering**: Query parameter filtering
- **Relationships**: Entity relationships and joins
- **Error Handling**: 404, 400, and other error responses

### 2. Unit Tests
Tests individual components in isolation:
- **Model Methods**: Database operations and business logic
- **Validation Functions**: Input validation utilities
- **Helper Functions**: Utility functions

### 3. Server Tests
Tests the overall server configuration:
- **Middleware**: CORS, security headers, input sanitization
- **Route Mounting**: Proper API route configuration
- **Error Handling**: Global error handling middleware
- **Health Checks**: Server status endpoints

## Test Data Factories

The `helpers.js` file provides factory functions for creating test data:

```javascript
// Create test entities
const user = createTestUser({ role_id: roleId });
const brand = createTestBrand({ industry: 'Technology' });
const role = createTestRole({ permissions: ['read', 'write'] });
const agent = createTestAgent({ brand_id: brandId });
```

## Test Database

Tests use a separate SQLite database (`tests/test.db`) that is:
- Created fresh for each test run
- Cleaned between individual tests
- Automatically removed after tests complete

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage report
npm run test:coverage

# Run only integration tests
npm run test:integration

# Run only unit tests
npm run test:unit
```

## Test Coverage

The test suite covers:

### Users API
- ✅ Create user with validation
- ✅ Get paginated users list
- ✅ Get user by ID with role info
- ✅ Update user data
- ✅ Delete user
- ✅ Activate/deactivate user
- ✅ Email/username uniqueness validation
- ✅ Role filtering

### Brands API
- ✅ Create brand with validation
- ✅ Get paginated brands list
- ✅ Get brand by ID with agent count
- ✅ Update brand data
- ✅ Delete brand (with agent check)
- ✅ Get brand agents
- ✅ Get industries list
- ✅ Industry filtering

### Roles API
- ✅ Create role with permissions
- ✅ Get paginated roles list
- ✅ Get role by ID with user count
- ✅ Update role and permissions
- ✅ Delete role (with user check)
- ✅ JSON permissions validation
- ✅ Role name uniqueness

### Agents API
- ✅ Create agent with configuration
- ✅ Get paginated agents list
- ✅ Get agent by ID with brand info
- ✅ Update agent configuration
- ✅ Delete agent
- ✅ Get agent types
- ✅ Type and brand filtering
- ✅ JSON configuration validation

### Database Models
- ✅ CRUD operations
- ✅ Relationship queries
- ✅ Data validation
- ✅ UUID generation
- ✅ Timestamp handling
- ✅ Soft delete operations

## Test Assertions

Common assertion patterns used:

```javascript
// Success responses
expectSuccessResponse(response, 201);
expectPaginatedResponse(response);

// Error responses
expectValidationError(response, 'field_name');
expectNotFoundError(response);

// Data validation
expectValidUUID(entity.uuid);
expectValidTimestamp(entity.created_at);
```

## Environment Variables

Test environment uses:
- `NODE_ENV=test`
- `DB_PATH=./tests/test.db`
- Isolated from development/production databases

## Best Practices

1. **Isolation**: Each test is independent and doesn't rely on other tests
2. **Cleanup**: Database is cleaned between tests
3. **Factories**: Use data factories for consistent test data
4. **Assertions**: Use helper functions for common assertions
5. **Coverage**: Aim for high test coverage of critical paths
6. **Documentation**: Tests serve as living documentation of API behavior

## Adding New Tests

When adding new features:

1. Create integration tests for new API endpoints
2. Add unit tests for new model methods
3. Update test factories if new entities are added
4. Ensure proper error handling tests
5. Test both success and failure scenarios
6. Update this documentation

## Troubleshooting

Common issues:

- **Database locks**: Ensure proper cleanup in test teardown
- **Async issues**: Use proper async/await patterns
- **Port conflicts**: Tests use in-memory database, no port conflicts
- **Test isolation**: Check that tests clean up properly between runs
