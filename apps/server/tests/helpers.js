const { v4: uuidv4 } = require('uuid');

// Test data factories
const createTestRole = (overrides = {}) => ({
  name: `test-role-${Date.now()}`,
  description: 'Test role description',
  permissions: JSON.stringify(['read', 'write']),
  ...overrides
});

const createTestUser = (overrides = {}) => ({
  username: `testuser-${Date.now()}`,
  email: `test-${Date.now()}@example.com`,
  password_hash: 'hashedpassword123',
  first_name: 'Test',
  last_name: 'User',
  ...overrides
});

const createTestBrand = (overrides = {}) => ({
  name: `Test Brand ${Date.now()}`,
  description: 'Test brand description',
  industry: 'Technology',
  website_url: 'https://example.com',
  ...overrides
});

const createTestAgent = (overrides = {}) => ({
  name: `Test Agent ${Date.now()}`,
  description: 'Test agent description',
  type: 'chatbot',
  configuration: JSON.stringify({ model: 'gpt-3.5-turbo' }),
  model_settings: JSON.stringify({ temperature: 0.7 }),
  ...overrides
});

const createTestAgentOutput = (overrides = {}) => ({
  input_text: 'Hello, how are you?',
  output_text: 'I am doing well, thank you!',
  output_type: 'text',
  session_id: uuidv4(),
  processing_time_ms: 150,
  metadata: JSON.stringify({ confidence: 0.95 }),
  ...overrides
});

const createTestActionHistory = (overrides = {}) => ({
  action_type: 'create',
  entity_type: 'user',
  entity_id: uuidv4(),
  description: 'Test action performed',
  metadata: JSON.stringify({ test: true }),
  ip_address: '127.0.0.1',
  user_agent: 'Jest Test Suite',
  ...overrides
});

const createTestLog = (overrides = {}) => ({
  level: 'info',
  message: 'Test log message',
  source: 'test',
  metadata: JSON.stringify({ test: true }),
  ...overrides
});

// Helper functions for API testing
const expectValidationError = (response, field) => {
  expect(response.status).toBe(400);
  expect(response.body.success).toBe(false);
  expect(response.body.error).toContain('required');
};

const expectNotFoundError = (response) => {
  expect(response.status).toBe(404);
  expect(response.body.success).toBe(false);
  expect(response.body.error).toContain('not found');
};

const expectSuccessResponse = (response, statusCode = 200) => {
  expect(response.status).toBe(statusCode);
  expect(response.body.success).toBe(true);
  expect(response.body.data).toBeDefined();
};

const expectPaginatedResponse = (response, statusCode = 200) => {
  expect(response.status).toBe(statusCode);
  expect(response.body.success).toBe(true);
  expect(response.body.data).toBeInstanceOf(Array);
  expect(response.body.pagination).toBeDefined();
  expect(response.body.pagination.page).toBeDefined();
  expect(response.body.pagination.limit).toBeDefined();
  expect(response.body.pagination.total).toBeDefined();
  expect(response.body.pagination.pages).toBeDefined();
};

const expectValidUUID = (uuid) => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  expect(uuid).toMatch(uuidRegex);
};

const expectValidTimestamp = (timestamp) => {
  expect(timestamp).toBeDefined();
  expect(new Date(timestamp).getTime()).not.toBeNaN();
};

module.exports = {
  createTestRole,
  createTestUser,
  createTestBrand,
  createTestAgent,
  createTestAgentOutput,
  createTestActionHistory,
  createTestLog,
  expectValidationError,
  expectNotFoundError,
  expectSuccessResponse,
  expectPaginatedResponse,
  expectValidUUID,
  expectValidTimestamp
};
