const request = require('supertest');
const express = require('express');
const cors = require('cors');
const brandsRoutes = require('../../src/routes/brands');
const errorHandler = require('../../src/middleware/errorHandler');
const {
  createTestBrand,
  expectValidationError,
  expectNotFoundError,
  expectSuccessResponse,
  expectPaginatedResponse,
  expectValidUUID,
  expectValidTimestamp
} = require('../helpers');

// Create test app
const app = express();
app.use(cors());
app.use(express.json());
app.use('/api/brands', brandsRoutes);
app.use(errorHandler);

describe('Brands API', () => {
  describe('POST /api/brands', () => {
    it('should create a new brand with valid data', async () => {
      const brandData = createTestBrand();

      const response = await request(app)
        .post('/api/brands')
        .send(brandData);

      expectSuccessResponse(response, 201);
      expect(response.body.data.name).toBe(brandData.name);
      expect(response.body.data.description).toBe(brandData.description);
      expect(response.body.data.industry).toBe(brandData.industry);
      expect(response.body.data.website_url).toBe(brandData.website_url);
      expectValidUUID(response.body.data.uuid);
      expectValidTimestamp(response.body.data.created_at);
      expectValidTimestamp(response.body.data.updated_at);
    });

    it('should return validation error for missing name', async () => {
      const response = await request(app)
        .post('/api/brands')
        .send({ description: 'Test description' });

      expectValidationError(response);
    });

    it('should return error for duplicate brand name', async () => {
      const brandData = createTestBrand();
      
      // Create first brand
      await request(app)
        .post('/api/brands')
        .send(brandData);

      // Try to create second brand with same name
      const response = await request(app)
        .post('/api/brands')
        .send(brandData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Brand name already exists');
    });
  });

  describe('GET /api/brands', () => {
    beforeEach(async () => {
      // Create test brands
      const industries = ['Technology', 'Healthcare', 'Finance'];
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/brands')
          .send(createTestBrand({ 
            industry: industries[i % industries.length] 
          }));
      }
    });

    it('should return paginated list of brands', async () => {
      const response = await request(app)
        .get('/api/brands')
        .query({ page: 1, limit: 3 });

      expectPaginatedResponse(response);
      expect(response.body.data.length).toBe(3);
      expect(response.body.pagination.total).toBe(5);
      expect(response.body.pagination.pages).toBe(2);
    });

    it('should filter brands by active status', async () => {
      const response = await request(app)
        .get('/api/brands')
        .query({ is_active: 'true' });

      expectPaginatedResponse(response);
      response.body.data.forEach(brand => {
        expect(brand.is_active).toBe(1);
      });
    });

    it('should filter brands by industry', async () => {
      const response = await request(app)
        .get('/api/brands')
        .query({ industry: 'Technology' });

      expectPaginatedResponse(response);
      response.body.data.forEach(brand => {
        expect(brand.industry).toBe('Technology');
      });
    });
  });

  describe('GET /api/brands/industries', () => {
    beforeEach(async () => {
      const industries = ['Technology', 'Healthcare', 'Finance'];
      for (const industry of industries) {
        await request(app)
          .post('/api/brands')
          .send(createTestBrand({ industry }));
      }
    });

    it('should return list of industries', async () => {
      const response = await request(app)
        .get('/api/brands/industries');

      expectSuccessResponse(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data).toContain('Technology');
      expect(response.body.data).toContain('Healthcare');
      expect(response.body.data).toContain('Finance');
    });
  });

  describe('GET /api/brands/:id', () => {
    let testBrand;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/brands')
        .send(createTestBrand());
      testBrand = response.body.data;
    });

    it('should return brand by ID', async () => {
      const response = await request(app)
        .get(`/api/brands/${testBrand.id}`);

      expectSuccessResponse(response);
      expect(response.body.data.id).toBe(testBrand.id);
      expect(response.body.data.name).toBe(testBrand.name);
    });

    it('should return 404 for non-existent brand', async () => {
      const response = await request(app)
        .get('/api/brands/99999');

      expectNotFoundError(response);
    });
  });

  describe('GET /api/brands/:id/agents', () => {
    let testBrand;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/brands')
        .send(createTestBrand());
      testBrand = response.body.data;
    });

    it('should return empty agents list for new brand', async () => {
      const response = await request(app)
        .get(`/api/brands/${testBrand.id}/agents`);

      expectSuccessResponse(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBe(0);
    });

    it('should return 404 for non-existent brand', async () => {
      const response = await request(app)
        .get('/api/brands/99999/agents');

      expectNotFoundError(response);
    });
  });

  describe('PUT /api/brands/:id', () => {
    let testBrand;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/brands')
        .send(createTestBrand());
      testBrand = response.body.data;
    });

    it('should update brand with valid data', async () => {
      const updateData = {
        description: 'Updated description',
        industry: 'Updated Industry'
      };

      const response = await request(app)
        .put(`/api/brands/${testBrand.id}`)
        .send(updateData);

      expectSuccessResponse(response);
      expect(response.body.data.description).toBe(updateData.description);
      expect(response.body.data.industry).toBe(updateData.industry);
    });

    it('should return 404 for non-existent brand', async () => {
      const response = await request(app)
        .put('/api/brands/99999')
        .send({ description: 'Updated' });

      expectNotFoundError(response);
    });

    it('should return error when updating to duplicate name', async () => {
      // Create another brand
      const anotherBrand = await request(app)
        .post('/api/brands')
        .send(createTestBrand());

      const response = await request(app)
        .put(`/api/brands/${testBrand.id}`)
        .send({ name: anotherBrand.body.data.name });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Brand name already exists');
    });
  });

  describe('DELETE /api/brands/:id', () => {
    let testBrand;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/brands')
        .send(createTestBrand());
      testBrand = response.body.data;
    });

    it('should delete brand', async () => {
      const response = await request(app)
        .delete(`/api/brands/${testBrand.id}`);

      expectSuccessResponse(response);
      expect(response.body.message).toContain('deleted successfully');

      // Verify brand is deleted
      const getResponse = await request(app)
        .get(`/api/brands/${testBrand.id}`);
      expectNotFoundError(getResponse);
    });

    it('should return 404 for non-existent brand', async () => {
      const response = await request(app)
        .delete('/api/brands/99999');

      expectNotFoundError(response);
    });
  });
});
