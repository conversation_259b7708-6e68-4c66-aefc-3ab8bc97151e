const request = require('supertest');
const express = require('express');
const cors = require('cors');
const rolesRoutes = require('../../src/routes/roles');
const errorHandler = require('../../src/middleware/errorHandler');
const {
  createTestRole,
  expectValidationError,
  expectNotFoundError,
  expectSuccessResponse,
  expectPaginatedResponse,
  expectValidUUID,
  expectValidTimestamp
} = require('../helpers');

// Create test app
const app = express();
app.use(cors());
app.use(express.json());
app.use('/api/roles', rolesRoutes);
app.use(errorHandler);

describe('Roles API', () => {
  describe('POST /api/roles', () => {
    it('should create a new role with valid data', async () => {
      const roleData = createTestRole();

      const response = await request(app)
        .post('/api/roles')
        .send(roleData);

      expectSuccessResponse(response, 201);
      expect(response.body.data.name).toBe(roleData.name);
      expect(response.body.data.description).toBe(roleData.description);
      expect(response.body.data.permissions).toBe(roleData.permissions);
      expectValidUUID(response.body.data.uuid);
      expectValidTimestamp(response.body.data.created_at);
      expectValidTimestamp(response.body.data.updated_at);
    });

    it('should create role with permissions array', async () => {
      const roleData = {
        ...createTestRole(),
        permissions: ['read', 'write', 'delete']
      };

      const response = await request(app)
        .post('/api/roles')
        .send(roleData);

      expectSuccessResponse(response, 201);
      expect(JSON.parse(response.body.data.permissions)).toEqual(roleData.permissions);
    });

    it('should return validation error for missing name', async () => {
      const response = await request(app)
        .post('/api/roles')
        .send({ description: 'Test description' });

      expectValidationError(response);
    });

    it('should return error for duplicate role name', async () => {
      const roleData = createTestRole();
      
      // Create first role
      await request(app)
        .post('/api/roles')
        .send(roleData);

      // Try to create second role with same name
      const response = await request(app)
        .post('/api/roles')
        .send(roleData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Role name already exists');
    });

    it('should return validation error for invalid JSON permissions', async () => {
      const roleData = {
        ...createTestRole(),
        permissions: 'invalid-json'
      };

      const response = await request(app)
        .post('/api/roles')
        .send(roleData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('valid JSON');
    });
  });

  describe('GET /api/roles', () => {
    beforeEach(async () => {
      // Create test roles
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/roles')
          .send(createTestRole());
      }
    });

    it('should return paginated list of roles', async () => {
      const response = await request(app)
        .get('/api/roles')
        .query({ page: 1, limit: 3 });

      expectPaginatedResponse(response);
      expect(response.body.data.length).toBe(3);
      expect(response.body.pagination.total).toBe(5);
      expect(response.body.pagination.pages).toBe(2);
    });

    it('should filter roles by active status', async () => {
      const response = await request(app)
        .get('/api/roles')
        .query({ is_active: 'true' });

      expectPaginatedResponse(response);
      response.body.data.forEach(role => {
        expect(role.is_active).toBe(1);
      });
    });

    it('should include user count in response', async () => {
      const response = await request(app)
        .get('/api/roles');

      expectPaginatedResponse(response);
      response.body.data.forEach(role => {
        expect(role.user_count).toBeDefined();
        expect(typeof role.user_count).toBe('number');
      });
    });
  });

  describe('GET /api/roles/:id', () => {
    let testRole;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/roles')
        .send(createTestRole());
      testRole = response.body.data;
    });

    it('should return role by ID', async () => {
      const response = await request(app)
        .get(`/api/roles/${testRole.id}`);

      expectSuccessResponse(response);
      expect(response.body.data.id).toBe(testRole.id);
      expect(response.body.data.name).toBe(testRole.name);
      expect(response.body.data.user_count).toBeDefined();
    });

    it('should return 404 for non-existent role', async () => {
      const response = await request(app)
        .get('/api/roles/99999');

      expectNotFoundError(response);
    });
  });

  describe('PUT /api/roles/:id', () => {
    let testRole;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/roles')
        .send(createTestRole());
      testRole = response.body.data;
    });

    it('should update role with valid data', async () => {
      const updateData = {
        description: 'Updated description',
        permissions: ['read', 'write', 'admin']
      };

      const response = await request(app)
        .put(`/api/roles/${testRole.id}`)
        .send(updateData);

      expectSuccessResponse(response);
      expect(response.body.data.description).toBe(updateData.description);
      expect(JSON.parse(response.body.data.permissions)).toEqual(updateData.permissions);
    });

    it('should return 404 for non-existent role', async () => {
      const response = await request(app)
        .put('/api/roles/99999')
        .send({ description: 'Updated' });

      expectNotFoundError(response);
    });

    it('should return error when updating to duplicate name', async () => {
      // Create another role
      const anotherRole = await request(app)
        .post('/api/roles')
        .send(createTestRole());

      const response = await request(app)
        .put(`/api/roles/${testRole.id}`)
        .send({ name: anotherRole.body.data.name });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Role name already exists');
    });

    it('should return validation error for invalid JSON permissions', async () => {
      const response = await request(app)
        .put(`/api/roles/${testRole.id}`)
        .send({ permissions: 'invalid-json' });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('valid JSON');
    });
  });

  describe('DELETE /api/roles/:id', () => {
    let testRole;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/roles')
        .send(createTestRole());
      testRole = response.body.data;
    });

    it('should delete role', async () => {
      const response = await request(app)
        .delete(`/api/roles/${testRole.id}`);

      expectSuccessResponse(response);
      expect(response.body.message).toContain('deleted successfully');

      // Verify role is deleted
      const getResponse = await request(app)
        .get(`/api/roles/${testRole.id}`);
      expectNotFoundError(getResponse);
    });

    it('should return 404 for non-existent role', async () => {
      const response = await request(app)
        .delete('/api/roles/99999');

      expectNotFoundError(response);
    });
  });
});
