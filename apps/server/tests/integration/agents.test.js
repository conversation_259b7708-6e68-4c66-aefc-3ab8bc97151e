const request = require('supertest');
const express = require('express');
const cors = require('cors');
const agentsRoutes = require('../../src/routes/agents');
const errorHandler = require('../../src/middleware/errorHandler');
const Brand = require('../../src/models/Brand');
const {
  createTestAgent,
  createTestBrand,
  expectValidationError,
  expectNotFoundError,
  expectSuccessResponse,
  expectPaginatedResponse,
  expectValidUUID,
  expectValidTimestamp
} = require('../helpers');

// Create test app
const app = express();
app.use(cors());
app.use(express.json());
app.use('/api/agents', agentsRoutes);
app.use(errorHandler);

describe('Agents API', () => {
  let testBrand;

  beforeEach(async () => {
    // Create a test brand for agent tests
    testBrand = await Brand.create(createTestBrand());
  });

  describe('POST /api/agents', () => {
    it('should create a new agent with valid data', async () => {
      const agentData = createTestAgent({ brand_id: testBrand.id });

      const response = await request(app)
        .post('/api/agents')
        .send(agentData);

      expectSuccessResponse(response, 201);
      expect(response.body.data.name).toBe(agentData.name);
      expect(response.body.data.description).toBe(agentData.description);
      expect(response.body.data.type).toBe(agentData.type);
      expect(response.body.data.brand_id).toBe(testBrand.id);
      expect(response.body.data.configuration).toBe(agentData.configuration);
      expect(response.body.data.model_settings).toBe(agentData.model_settings);
      expectValidUUID(response.body.data.uuid);
      expectValidTimestamp(response.body.data.created_at);
      expectValidTimestamp(response.body.data.updated_at);
    });

    it('should create agent with configuration object', async () => {
      const agentData = {
        ...createTestAgent({ brand_id: testBrand.id }),
        configuration: { model: 'gpt-4', temperature: 0.8 },
        model_settings: { max_tokens: 1000 }
      };

      const response = await request(app)
        .post('/api/agents')
        .send(agentData);

      expectSuccessResponse(response, 201);
      expect(JSON.parse(response.body.data.configuration)).toEqual(agentData.configuration);
      expect(JSON.parse(response.body.data.model_settings)).toEqual(agentData.model_settings);
    });

    it('should return validation error for missing required fields', async () => {
      const response = await request(app)
        .post('/api/agents')
        .send({ description: 'Test description' });

      expectValidationError(response);
    });

    it('should return error for duplicate agent name', async () => {
      const agentData = createTestAgent({ brand_id: testBrand.id });
      
      // Create first agent
      await request(app)
        .post('/api/agents')
        .send(agentData);

      // Try to create second agent with same name
      const response = await request(app)
        .post('/api/agents')
        .send(agentData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Agent name already exists');
    });

    it('should return validation error for invalid JSON configuration', async () => {
      const agentData = {
        ...createTestAgent({ brand_id: testBrand.id }),
        configuration: 'invalid-json'
      };

      const response = await request(app)
        .post('/api/agents')
        .send(agentData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('valid JSON');
    });
  });

  describe('GET /api/agents/types', () => {
    beforeEach(async () => {
      const types = ['chatbot', 'voice', 'task_automation'];
      for (const type of types) {
        await request(app)
          .post('/api/agents')
          .send(createTestAgent({ type, brand_id: testBrand.id }));
      }
    });

    it('should return list of agent types', async () => {
      const response = await request(app)
        .get('/api/agents/types');

      expectSuccessResponse(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data).toContain('chatbot');
      expect(response.body.data).toContain('voice');
      expect(response.body.data).toContain('task_automation');
    });
  });

  describe('GET /api/agents', () => {
    beforeEach(async () => {
      // Create test agents
      const types = ['chatbot', 'voice', 'task_automation'];
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/agents')
          .send(createTestAgent({ 
            type: types[i % types.length],
            brand_id: testBrand.id 
          }));
      }
    });

    it('should return paginated list of agents', async () => {
      const response = await request(app)
        .get('/api/agents')
        .query({ page: 1, limit: 3 });

      expectPaginatedResponse(response);
      expect(response.body.data.length).toBe(3);
      expect(response.body.pagination.total).toBe(5);
      expect(response.body.pagination.pages).toBe(2);
    });

    it('should filter agents by active status', async () => {
      const response = await request(app)
        .get('/api/agents')
        .query({ is_active: 'true' });

      expectPaginatedResponse(response);
      response.body.data.forEach(agent => {
        expect(agent.is_active).toBe(1);
      });
    });

    it('should filter agents by type', async () => {
      const response = await request(app)
        .get('/api/agents')
        .query({ type: 'chatbot' });

      expectPaginatedResponse(response);
      response.body.data.forEach(agent => {
        expect(agent.type).toBe('chatbot');
      });
    });

    it('should filter agents by brand', async () => {
      const response = await request(app)
        .get('/api/agents')
        .query({ brand_id: testBrand.id });

      expectPaginatedResponse(response);
      response.body.data.forEach(agent => {
        expect(agent.brand_id).toBe(testBrand.id);
      });
    });

    it('should include brand information in response', async () => {
      const response = await request(app)
        .get('/api/agents');

      expectPaginatedResponse(response);
      response.body.data.forEach(agent => {
        expect(agent.brand_name).toBe(testBrand.name);
      });
    });
  });

  describe('GET /api/agents/:id', () => {
    let testAgent;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/agents')
        .send(createTestAgent({ brand_id: testBrand.id }));
      testAgent = response.body.data;
    });

    it('should return agent by ID', async () => {
      const response = await request(app)
        .get(`/api/agents/${testAgent.id}`);

      expectSuccessResponse(response);
      expect(response.body.data.id).toBe(testAgent.id);
      expect(response.body.data.name).toBe(testAgent.name);
      expect(response.body.data.brand_name).toBe(testBrand.name);
    });

    it('should return 404 for non-existent agent', async () => {
      const response = await request(app)
        .get('/api/agents/99999');

      expectNotFoundError(response);
    });
  });

  describe('PUT /api/agents/:id', () => {
    let testAgent;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/agents')
        .send(createTestAgent({ brand_id: testBrand.id }));
      testAgent = response.body.data;
    });

    it('should update agent with valid data', async () => {
      const updateData = {
        description: 'Updated description',
        type: 'voice',
        configuration: { model: 'gpt-4' }
      };

      const response = await request(app)
        .put(`/api/agents/${testAgent.id}`)
        .send(updateData);

      expectSuccessResponse(response);
      expect(response.body.data.description).toBe(updateData.description);
      expect(response.body.data.type).toBe(updateData.type);
      expect(JSON.parse(response.body.data.configuration)).toEqual(updateData.configuration);
    });

    it('should return 404 for non-existent agent', async () => {
      const response = await request(app)
        .put('/api/agents/99999')
        .send({ description: 'Updated' });

      expectNotFoundError(response);
    });

    it('should return error when updating to duplicate name', async () => {
      // Create another agent
      const anotherAgent = await request(app)
        .post('/api/agents')
        .send(createTestAgent({ brand_id: testBrand.id }));

      const response = await request(app)
        .put(`/api/agents/${testAgent.id}`)
        .send({ name: anotherAgent.body.data.name });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Agent name already exists');
    });
  });

  describe('DELETE /api/agents/:id', () => {
    let testAgent;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/agents')
        .send(createTestAgent({ brand_id: testBrand.id }));
      testAgent = response.body.data;
    });

    it('should delete agent', async () => {
      const response = await request(app)
        .delete(`/api/agents/${testAgent.id}`);

      expectSuccessResponse(response);
      expect(response.body.message).toContain('deleted successfully');

      // Verify agent is deleted
      const getResponse = await request(app)
        .get(`/api/agents/${testAgent.id}`);
      expectNotFoundError(getResponse);
    });

    it('should return 404 for non-existent agent', async () => {
      const response = await request(app)
        .delete('/api/agents/99999');

      expectNotFoundError(response);
    });
  });
});
