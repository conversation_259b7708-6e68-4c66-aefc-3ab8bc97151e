const request = require('supertest');
const express = require('express');
const cors = require('cors');
const usersRoutes = require('../../src/routes/users');
const errorHandler = require('../../src/middleware/errorHandler');
const Role = require('../../src/models/Role');
const {
  createTestUser,
  createTestRole,
  expectValidationError,
  expectNotFoundError,
  expectSuccessResponse,
  expectPaginatedResponse,
  expectValidUUID,
  expectValidTimestamp
} = require('../helpers');

// Create test app
const app = express();
app.use(cors());
app.use(express.json());
app.use('/api/users', usersRoutes);
app.use(errorHandler);

describe('Users API', () => {
  let testRole;

  beforeEach(async () => {
    // Create a test role for user tests
    testRole = await Role.create(createTestRole());
  });

  describe('POST /api/users', () => {
    it('should create a new user with valid data', async () => {
      const userData = createTestUser({ role_id: testRole.id });

      const response = await request(app)
        .post('/api/users')
        .send(userData);

      expectSuccessResponse(response, 201);
      expect(response.body.data.username).toBe(userData.username);
      expect(response.body.data.email).toBe(userData.email);
      expect(response.body.data.first_name).toBe(userData.first_name);
      expect(response.body.data.last_name).toBe(userData.last_name);
      expect(response.body.data.role_id).toBe(testRole.id);
      expectValidUUID(response.body.data.uuid);
      expectValidTimestamp(response.body.data.created_at);
      expectValidTimestamp(response.body.data.updated_at);
    });

    it('should return validation error for missing required fields', async () => {
      const response = await request(app)
        .post('/api/users')
        .send({});

      expectValidationError(response);
    });

    it('should return validation error for invalid email', async () => {
      const userData = createTestUser({ 
        email: 'invalid-email',
        role_id: testRole.id 
      });

      const response = await request(app)
        .post('/api/users')
        .send(userData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('email');
    });

    it('should return error for duplicate email', async () => {
      const userData = createTestUser({ role_id: testRole.id });
      
      // Create first user
      await request(app)
        .post('/api/users')
        .send(userData);

      // Try to create second user with same email
      const response = await request(app)
        .post('/api/users')
        .send({ ...userData, username: 'different-username' });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Email already exists');
    });

    it('should return error for duplicate username', async () => {
      const userData = createTestUser({ role_id: testRole.id });
      
      // Create first user
      await request(app)
        .post('/api/users')
        .send(userData);

      // Try to create second user with same username
      const response = await request(app)
        .post('/api/users')
        .send({ ...userData, email: '<EMAIL>' });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Username already exists');
    });
  });

  describe('GET /api/users', () => {
    beforeEach(async () => {
      // Create test users
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/users')
          .send(createTestUser({ role_id: testRole.id }));
      }
    });

    it('should return paginated list of users', async () => {
      const response = await request(app)
        .get('/api/users')
        .query({ page: 1, limit: 3 });

      expectPaginatedResponse(response);
      expect(response.body.data.length).toBe(3);
      expect(response.body.pagination.total).toBe(5);
      expect(response.body.pagination.pages).toBe(2);
    });

    it('should filter users by active status', async () => {
      const response = await request(app)
        .get('/api/users')
        .query({ is_active: 'true' });

      expectPaginatedResponse(response);
      response.body.data.forEach(user => {
        expect(user.is_active).toBe(1);
      });
    });

    it('should filter users by role', async () => {
      const response = await request(app)
        .get('/api/users')
        .query({ role_id: testRole.id });

      expectPaginatedResponse(response);
      response.body.data.forEach(user => {
        expect(user.role_id).toBe(testRole.id);
      });
    });
  });

  describe('GET /api/users/:id', () => {
    let testUser;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/users')
        .send(createTestUser({ role_id: testRole.id }));
      testUser = response.body.data;
    });

    it('should return user by ID', async () => {
      const response = await request(app)
        .get(`/api/users/${testUser.id}`);

      expectSuccessResponse(response);
      expect(response.body.data.id).toBe(testUser.id);
      expect(response.body.data.role_name).toBe(testRole.name);
    });

    it('should return 404 for non-existent user', async () => {
      const response = await request(app)
        .get('/api/users/99999');

      expectNotFoundError(response);
    });
  });

  describe('PUT /api/users/:id', () => {
    let testUser;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/users')
        .send(createTestUser({ role_id: testRole.id }));
      testUser = response.body.data;
    });

    it('should update user with valid data', async () => {
      const updateData = {
        first_name: 'Updated',
        last_name: 'Name'
      };

      const response = await request(app)
        .put(`/api/users/${testUser.id}`)
        .send(updateData);

      expectSuccessResponse(response);
      expect(response.body.data.first_name).toBe(updateData.first_name);
      expect(response.body.data.last_name).toBe(updateData.last_name);
    });

    it('should return 404 for non-existent user', async () => {
      const response = await request(app)
        .put('/api/users/99999')
        .send({ first_name: 'Updated' });

      expectNotFoundError(response);
    });
  });

  describe('DELETE /api/users/:id', () => {
    let testUser;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/users')
        .send(createTestUser({ role_id: testRole.id }));
      testUser = response.body.data;
    });

    it('should delete user', async () => {
      const response = await request(app)
        .delete(`/api/users/${testUser.id}`);

      expectSuccessResponse(response);
      expect(response.body.message).toContain('deleted successfully');

      // Verify user is deleted
      const getResponse = await request(app)
        .get(`/api/users/${testUser.id}`);
      expectNotFoundError(getResponse);
    });

    it('should return 404 for non-existent user', async () => {
      const response = await request(app)
        .delete('/api/users/99999');

      expectNotFoundError(response);
    });
  });

  describe('POST /api/users/:id/deactivate', () => {
    let testUser;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/users')
        .send(createTestUser({ role_id: testRole.id }));
      testUser = response.body.data;
    });

    it('should deactivate user', async () => {
      const response = await request(app)
        .post(`/api/users/${testUser.id}/deactivate`);

      expectSuccessResponse(response);
      expect(response.body.data.is_active).toBe(0);
    });
  });

  describe('POST /api/users/:id/activate', () => {
    let testUser;

    beforeEach(async () => {
      const response = await request(app)
        .post('/api/users')
        .send(createTestUser({ role_id: testRole.id }));
      testUser = response.body.data;

      // Deactivate first
      await request(app)
        .post(`/api/users/${testUser.id}/deactivate`);
    });

    it('should activate user', async () => {
      const response = await request(app)
        .post(`/api/users/${testUser.id}/activate`);

      expectSuccessResponse(response);
      expect(response.body.data.is_active).toBe(1);
    });
  });
});
