const fs = require('fs');
const path = require('path');
const database = require('../src/database/connection');

// Test database path - use memory database for tests
const TEST_DB_PATH = ':memory:';

beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  process.env.DB_PATH = TEST_DB_PATH;

  // Connect to test database
  await database.connect();

  // Initialize test database schema
  const schemaPath = path.join(__dirname, '../src/database/schema.sql');
  const schema = fs.readFileSync(schemaPath, 'utf8');

  const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);

  for (const statement of statements) {
    await database.run(statement.trim());
  }
});

afterAll(async () => {
  // Close database connection
  await database.close();

  // Memory database is automatically cleaned up
});

beforeEach(async () => {
  // Clear all tables before each test
  const tables = [
    'Logs',
    'Action_History', 
    'Agent_Outputs',
    'Agents',
    'Users',
    'Brands',
    'Roles'
  ];
  
  for (const table of tables) {
    await database.run(`DELETE FROM ${table}`);
  }
});
