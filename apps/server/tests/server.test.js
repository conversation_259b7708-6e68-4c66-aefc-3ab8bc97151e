const request = require('supertest');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

// Import all routes
const usersRoutes = require('../src/routes/users');
const brandsRoutes = require('../src/routes/brands');
const rolesRoutes = require('../src/routes/roles');
const agentsRoutes = require('../src/routes/agents');
const errorHandler = require('../src/middleware/errorHandler');
const { sanitizeInput } = require('../src/middleware/validation');

// Create test app that mirrors the main server
const createTestApp = () => {
  const app = express();

  // Security middleware
  app.use(helmet());

  // CORS configuration
  app.use(cors({
    origin: 'http://localhost:5173',
    credentials: true
  }));

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Input sanitization
  app.use(sanitizeInput);

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      success: true,
      message: 'Server is running',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    });
  });

  // API routes
  app.use('/api/users', usersRoutes);
  app.use('/api/brands', brandsRoutes);
  app.use('/api/roles', rolesRoutes);
  app.use('/api/agents', agentsRoutes);

  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      success: false,
      error: 'Route not found'
    });
  });

  // Error handling middleware (must be last)
  app.use(errorHandler);

  return app;
};

describe('Server Integration Tests', () => {
  let app;

  beforeAll(() => {
    app = createTestApp();
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Server is running');
      expect(response.body.timestamp).toBeDefined();
      expect(response.body.environment).toBeDefined();
    });
  });

  describe('CORS Headers', () => {
    it('should include CORS headers', async () => {
      const response = await request(app)
        .get('/health')
        .set('Origin', 'http://localhost:5173');

      expect(response.headers['access-control-allow-origin']).toBe('http://localhost:5173');
    });
  });

  describe('Security Headers', () => {
    it('should include security headers from helmet', async () => {
      const response = await request(app)
        .get('/health');

      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBeDefined();
    });
  });

  describe('API Routes', () => {
    it('should have users routes mounted', async () => {
      const response = await request(app)
        .get('/api/users');

      // Should not return 404 (route exists)
      expect(response.status).not.toBe(404);
    });

    it('should have brands routes mounted', async () => {
      const response = await request(app)
        .get('/api/brands');

      expect(response.status).not.toBe(404);
    });

    it('should have roles routes mounted', async () => {
      const response = await request(app)
        .get('/api/roles');

      expect(response.status).not.toBe(404);
    });

    it('should have agents routes mounted', async () => {
      const response = await request(app)
        .get('/api/agents');

      expect(response.status).not.toBe(404);
    });
  });

  describe('404 Handler', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/non-existent-route');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Route not found');
    });
  });

  describe('Input Sanitization', () => {
    it('should sanitize input data', async () => {
      const response = await request(app)
        .post('/api/users')
        .send({
          username: '  testuser  ',
          email: '  <EMAIL>  ',
          password_hash: 'password'
        });

      // The middleware should trim whitespace
      // Response will vary based on validation, but input should be sanitized
      expect(response.status).toBeDefined();
    });
  });

  describe('JSON Body Parsing', () => {
    it('should parse JSON request bodies', async () => {
      const response = await request(app)
        .post('/api/roles')
        .send({
          name: 'test-role',
          permissions: ['read', 'write']
        })
        .set('Content-Type', 'application/json');

      // Should not return 400 for malformed JSON
      expect(response.status).not.toBe(400);
    });

    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/roles')
        .send('{"invalid": json}')
        .set('Content-Type', 'application/json');

      expect(response.status).toBe(400);
    });
  });

  describe('Error Handling', () => {
    it('should handle validation errors gracefully', async () => {
      const response = await request(app)
        .post('/api/users')
        .send({}); // Missing required fields

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });
});
