const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

/**
 * Generate JWT token for user
 */
const generateToken = (user) => {
  const payload = {
    id: user.id,
    uuid: user.uuid,
    username: user.username,
    email: user.email,
    role: {
      id: user.role_id,
      name: user.role_name,
      permissions: user.permissions ? JSON.parse(user.permissions) : []
    }
  };

  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  });
};

/**
 * Verify JWT token
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    throw new Error('Invalid token');
  }
};

/**
 * Hash password
 */
const hashPassword = async (password) => {
  const rounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  return await bcrypt.hash(password, rounds);
};

/**
 * Compare password with hash
 */
const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

/**
 * Check if user has permission
 */
const hasPermission = (userPermissions, requiredPermission) => {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return false;
  }
  return userPermissions.includes(requiredPermission);
};

/**
 * Check if user has any of the required permissions
 */
const hasAnyPermission = (userPermissions, requiredPermissions) => {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return false;
  }
  return requiredPermissions.some(permission => userPermissions.includes(permission));
};

/**
 * Check if user has all required permissions
 */
const hasAllPermissions = (userPermissions, requiredPermissions) => {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return false;
  }
  return requiredPermissions.every(permission => userPermissions.includes(permission));
};

/**
 * Role hierarchy for permission inheritance
 */
const ROLE_HIERARCHY = {
  'super_admin': ['brand_admin', 'editor', 'operator'],
  'brand_admin': ['editor', 'operator'],
  'editor': ['operator'],
  'operator': []
};

/**
 * Check if user role has access to target role's permissions
 */
const hasRoleAccess = (userRole, targetRole) => {
  if (userRole === targetRole) return true;
  
  const hierarchy = ROLE_HIERARCHY[userRole] || [];
  return hierarchy.includes(targetRole);
};

/**
 * Get effective permissions for user (including inherited from role hierarchy)
 */
const getEffectivePermissions = (userRole, userPermissions) => {
  let effectivePermissions = [...userPermissions];
  
  // Add permissions from lower roles in hierarchy
  const lowerRoles = ROLE_HIERARCHY[userRole] || [];
  // This would need to be expanded with actual role permission lookup
  // For now, we'll use the direct permissions
  
  return [...new Set(effectivePermissions)]; // Remove duplicates
};

module.exports = {
  generateToken,
  verifyToken,
  hashPassword,
  comparePassword,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  hasRoleAccess,
  getEffectivePermissions,
  ROLE_HIERARCHY
};
