const { verifyToken, hasPermission, hasAnyPermission, hasRoleAccess } = require('../utils/auth');
const User = require('../models/User');

/**
 * Middleware to authenticate user via JWT token
 */
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Access token required'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    try {
      const decoded = verifyToken(token);

      // Get fresh user data to ensure current permissions
      const user = await User.findWithRole(decoded.id);

      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'User not found'
        });
      }

      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          error: 'Account is deactivated'
        });
      }

      // Attach user info to request
      req.user = {
        id: user.id,
        uuid: user.uuid,
        username: user.username,
        email: user.email,
        brand_id: user.brand_id,
        brand: user.brand_id ? {
          id: user.brand_id,
          uuid: user.brand_uuid,
          name: user.brand_name,
          logo_url: user.brand_logo
        } : null,
        role: {
          id: user.role_id,
          name: user.role_name,
          permissions: user.permissions ? JSON.parse(user.permissions) : []
        }
      };

      next();
    } catch (tokenError) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      error: 'Authentication failed'
    });
  }
};

/**
 * Middleware to check if user has required permission
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userPermissions = req.user.role.permissions || [];
    
    if (!hasPermission(userPermissions, permission)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
    }

    next();
  };
};

/**
 * Middleware to check if user has any of the required permissions
 */
const requireAnyPermission = (permissions) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userPermissions = req.user.role.permissions || [];
    
    if (!hasAnyPermission(userPermissions, permissions)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
    }

    next();
  };
};

/**
 * Middleware to check if user has specific role
 */
const requireRole = (role) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (req.user.role.name !== role) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient role privileges'
      });
    }

    next();
  };
};

/**
 * Middleware to check if user has role access (including hierarchy)
 */
const requireRoleAccess = (targetRole) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userRole = req.user.role.name;
    
    if (!hasRoleAccess(userRole, targetRole)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient role access'
      });
    }

    next();
  };
};

/**
 * Middleware for optional authentication (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = verifyToken(token);
      const user = await User.findWithRole(decoded.id);
      
      if (user && user.is_active) {
        req.user = {
          id: user.id,
          uuid: user.uuid,
          username: user.username,
          email: user.email,
          brand_id: user.brand_id,
          brand: user.brand_id ? {
            id: user.brand_id,
            uuid: user.brand_uuid,
            name: user.brand_name,
            logo_url: user.brand_logo
          } : null,
          role: {
            id: user.role_id,
            name: user.role_name,
            permissions: user.permissions ? JSON.parse(user.permissions) : []
          }
        };
      }
    } catch (tokenError) {
      // Invalid token, but continue without authentication
    }

    next();
  } catch (error) {
    console.error('Optional auth error:', error);
    next(); // Continue without authentication
  }
};

/**
 * Middleware to check if user can access their own resource or has admin privileges
 */
const requireOwnershipOrAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const resourceUserId = req.params.userId || req.params.id;
  const userPermissions = req.user.role.permissions || [];
  
  // Allow if user is accessing their own resource
  if (req.user.id.toString() === resourceUserId.toString()) {
    return next();
  }

  // Allow if user has admin permissions
  if (hasAnyPermission(userPermissions, ['manage_users', 'manage_brand_users'])) {
    return next();
  }

  return res.status(403).json({
    success: false,
    error: 'Access denied'
  });
};

module.exports = {
  authenticate,
  requirePermission,
  requireAnyPermission,
  requireRole,
  requireRoleAccess,
  optionalAuth,
  requireOwnershipOrAdmin
};
