const { hasPermission } = require('../utils/auth');

/**
 * Middleware to ensure data queries are scoped to user's brand
 * Super admins can access all brands, others are restricted to their brand
 */
const requireBrandScoping = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const userPermissions = req.user.role.permissions || [];
  const isSuperAdmin = hasPermission(userPermissions, 'view_all_data');

  // Super admins can access all brands
  if (isSuperAdmin) {
    req.brandScope = 'all';
    return next();
  }

  // Other users are restricted to their brand
  if (!req.user.brand_id) {
    return res.status(403).json({
      success: false,
      error: 'User not assigned to any brand'
    });
  }

  req.brandScope = req.user.brand_id;
  next();
};

/**
 * Middleware to validate brand access for specific operations
 * Checks if user can access the requested brand
 */
const validateBrandAccess = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const userPermissions = req.user.role.permissions || [];
  const isSuperAdmin = hasPermission(userPermissions, 'view_all_data');
  const requestedBrandId = req.params.brandId || req.body.brand_id;

  // Super admins can access any brand
  if (isSuperAdmin) {
    return next();
  }

  // Brand admins and below can only access their own brand
  if (!req.user.brand_id) {
    return res.status(403).json({
      success: false,
      error: 'User not assigned to any brand'
    });
  }

  if (requestedBrandId && parseInt(requestedBrandId) !== req.user.brand_id) {
    return res.status(403).json({
      success: false,
      error: 'Access denied to this brand'
    });
  }

  next();
};

/**
 * Middleware to ensure brand admin can only manage users within their brand
 */
const requireBrandUserAccess = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const userPermissions = req.user.role.permissions || [];
  const isSuperAdmin = hasPermission(userPermissions, 'manage_users');
  const isBrandAdmin = hasPermission(userPermissions, 'manage_brand_users');

  // Super admins can manage all users
  if (isSuperAdmin) {
    return next();
  }

  // Brand admins can only manage users in their brand
  if (isBrandAdmin) {
    if (!req.user.brand_id) {
      return res.status(403).json({
        success: false,
        error: 'Brand admin not assigned to any brand'
      });
    }
    
    // Add brand restriction to request for downstream processing
    req.brandUserRestriction = req.user.brand_id;
    return next();
  }

  return res.status(403).json({
    success: false,
    error: 'Insufficient permissions for user management'
  });
};

/**
 * Helper function to apply brand scoping to SQL queries
 */
const applyBrandScope = (baseQuery, brandScope, tableAlias = '') => {
  if (brandScope === 'all') {
    return baseQuery;
  }

  const prefix = tableAlias ? `${tableAlias}.` : '';
  const brandCondition = `${prefix}brand_id = ${brandScope}`;

  if (baseQuery.toLowerCase().includes('where')) {
    return `${baseQuery} AND ${brandCondition}`;
  } else {
    return `${baseQuery} WHERE ${brandCondition}`;
  }
};

module.exports = {
  requireBrandScoping,
  validateBrandAccess,
  requireBrandUserAccess,
  applyBrandScope
};
