const Log = require('../models/Log');

const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Log the error to database
  logError(err, req).catch(logErr => {
    console.error('Failed to log error to database:', logErr);
  });

  // Default error
  let error = {
    message: err.message || 'Internal Server Error',
    status: err.status || 500
  };

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = { message, status: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = { message, status: 400 };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = { message, status: 400 };
  }

  // SQLite constraint error
  if (err.code === 'SQLITE_CONSTRAINT') {
    const message = 'Database constraint violation';
    error = { message, status: 400 };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = { message, status: 401 };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = { message, status: 401 };
  }

  res.status(error.status).json({
    success: false,
    error: error.message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

async function logError(err, req) {
  try {
    await Log.create({
      level: 'error',
      message: err.message,
      source: 'api',
      metadata: JSON.stringify({
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.body,
        params: req.params,
        query: req.query
      }),
      stack_trace: err.stack,
      user_id: req.user ? req.user.id : null
    });
  } catch (logError) {
    console.error('Error logging to database:', logError);
  }
}

module.exports = errorHandler;
