const express = require('express');
const router = express.Router();
const Agent = require('../models/Agent');
const ActionHistory = require('../models/ActionHistory');
const { validateRequired, validateJSON, validatePagination, sanitizeInput } = require('../middleware/validation');

// GET /api/agents/types - Get all agent types
router.get('/types', async (req, res, next) => {
  try {
    const types = await Agent.getTypes();

    res.json({
      success: true,
      data: types
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/agents - Get all agents
router.get('/', validatePagination, sanitizeInput, async (req, res, next) => {
  try {
    const { page, limit, offset } = req.pagination;
    const { is_active, type, brand_id } = req.query;

    const options = { limit, offset };
    if (is_active !== undefined || type || brand_id) {
      options.where = {};
      if (is_active !== undefined) options.where.is_active = is_active === 'true' ? 1 : 0;
      if (type) options.where.type = type;
      if (brand_id) options.where.brand_id = brand_id;
    }

    const agents = await Agent.findAllWithBrands(options);
    const total = await Agent.count(options.where || {});

    res.json({
      success: true,
      data: agents,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/agents/:id - Get agent by ID
router.get('/:id', async (req, res, next) => {
  try {
    const agent = await Agent.findWithBrand(req.params.id);

    if (!agent) {
      return res.status(404).json({
        success: false,
        error: 'Agent not found'
      });
    }

    res.json({
      success: true,
      data: agent
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/agents - Create new agent
router.post('/', 
  sanitizeInput,
  validateRequired(['name', 'type']),
  validateJSON(['configuration', 'model_settings']),
  async (req, res, next) => {
    try {
      const { name, description, type, brand_id, configuration, model_settings } = req.body;

      const existingAgent = await Agent.findByName(name);
      if (existingAgent) {
        return res.status(400).json({
          success: false,
          error: 'Agent name already exists'
        });
      }

      const agentData = {
        name,
        description,
        type,
        brand_id,
        configuration: typeof configuration === 'string' ? configuration : JSON.stringify(configuration || {}),
        model_settings: typeof model_settings === 'string' ? model_settings : JSON.stringify(model_settings || {})
      };

      const agent = await Agent.create(agentData);

      await ActionHistory.create({
        action_type: 'create',
        entity_type: 'agent',
        entity_id: agent.uuid,
        description: `Agent ${name} created`
      });

      res.status(201).json({
        success: true,
        data: agent
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/agents/:id - Update agent
router.put('/:id', 
  sanitizeInput,
  validateJSON(['configuration', 'model_settings']),
  async (req, res, next) => {
    try {
      const { name, description, type, brand_id, configuration, model_settings, is_active } = req.body;

      const existingAgent = await Agent.findById(req.params.id);
      if (!existingAgent) {
        return res.status(404).json({
          success: false,
          error: 'Agent not found'
        });
      }

      if (name && name !== existingAgent.name) {
        const nameExists = await Agent.findByName(name);
        if (nameExists) {
          return res.status(400).json({
            success: false,
            error: 'Agent name already exists'
          });
        }
      }

      const updateData = {};
      if (name) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (type) updateData.type = type;
      if (brand_id !== undefined) updateData.brand_id = brand_id;
      if (configuration) updateData.configuration = typeof configuration === 'string' ? configuration : JSON.stringify(configuration);
      if (model_settings) updateData.model_settings = typeof model_settings === 'string' ? model_settings : JSON.stringify(model_settings);
      if (is_active !== undefined) updateData.is_active = is_active;

      const agent = await Agent.update(req.params.id, updateData);

      await ActionHistory.create({
        action_type: 'update',
        entity_type: 'agent',
        entity_id: agent.uuid,
        description: `Agent ${agent.name} updated`
      });

      res.json({
        success: true,
        data: agent
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/agents/:id - Delete agent
router.delete('/:id', async (req, res, next) => {
  try {
    const agent = await Agent.findById(req.params.id);
    if (!agent) {
      return res.status(404).json({
        success: false,
        error: 'Agent not found'
      });
    }

    await Agent.delete(req.params.id);

    await ActionHistory.create({
      action_type: 'delete',
      entity_type: 'agent',
      entity_id: agent.uuid,
      description: `Agent ${agent.name} deleted`
    });

    res.json({
      success: true,
      message: 'Agent deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
