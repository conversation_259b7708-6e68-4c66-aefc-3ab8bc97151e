const express = require('express');
const router = express.Router();
const Role = require('../models/Role');
const ActionHistory = require('../models/ActionHistory');
const { validateRequired, validateJSON, validatePagination, sanitizeInput } = require('../middleware/validation');

// GET /api/roles - Get all roles
router.get('/', validatePagination, sanitizeInput, async (req, res, next) => {
  try {
    const { page, limit, offset } = req.pagination;
    const { is_active } = req.query;

    const options = { limit, offset };
    if (is_active !== undefined) {
      options.where = { is_active: is_active === 'true' ? 1 : 0 };
    }

    const roles = await Role.findAllWithUserCounts(options);
    const total = await Role.count(options.where || {});

    res.json({
      success: true,
      data: roles,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/roles/:id - Get role by ID
router.get('/:id', async (req, res, next) => {
  try {
    const role = await Role.findWithUserCount(req.params.id);
    
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    res.json({
      success: true,
      data: role
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/roles - Create new role
router.post('/', 
  sanitizeInput,
  validateRequired(['name']),
  validateJSON(['permissions']),
  async (req, res, next) => {
    try {
      const { name, description, permissions } = req.body;

      const existingRole = await Role.findByName(name);
      if (existingRole) {
        return res.status(400).json({
          success: false,
          error: 'Role name already exists'
        });
      }

      const roleData = {
        name,
        description,
        permissions: typeof permissions === 'string' ? permissions : JSON.stringify(permissions || [])
      };

      const role = await Role.create(roleData);

      await ActionHistory.create({
        action_type: 'create',
        entity_type: 'role',
        entity_id: role.uuid,
        description: `Role ${name} created`
      });

      res.status(201).json({
        success: true,
        data: role
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/roles/:id - Update role
router.put('/:id', 
  sanitizeInput,
  validateJSON(['permissions']),
  async (req, res, next) => {
    try {
      const { name, description, permissions, is_active } = req.body;

      const existingRole = await Role.findById(req.params.id);
      if (!existingRole) {
        return res.status(404).json({
          success: false,
          error: 'Role not found'
        });
      }

      if (name && name !== existingRole.name) {
        const nameExists = await Role.findByName(name);
        if (nameExists) {
          return res.status(400).json({
            success: false,
            error: 'Role name already exists'
          });
        }
      }

      const updateData = {};
      if (name) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (permissions) updateData.permissions = typeof permissions === 'string' ? permissions : JSON.stringify(permissions);
      if (is_active !== undefined) updateData.is_active = is_active;

      const role = await Role.update(req.params.id, updateData);

      await ActionHistory.create({
        action_type: 'update',
        entity_type: 'role',
        entity_id: role.uuid,
        description: `Role ${role.name} updated`
      });

      res.json({
        success: true,
        data: role
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/roles/:id - Delete role
router.delete('/:id', async (req, res, next) => {
  try {
    const role = await Role.findById(req.params.id);
    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    const users = await Role.getUsersByRole(req.params.id);
    if (users.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete role with existing users'
      });
    }

    await Role.delete(req.params.id);

    await ActionHistory.create({
      action_type: 'delete',
      entity_type: 'role',
      entity_id: role.uuid,
      description: `Role ${role.name} deleted`
    });

    res.json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
