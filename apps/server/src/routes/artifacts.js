const express = require('express');
const router = express.Router();
const Artifact = require('../models/Artifact');

/**
 * @route POST /api/artifacts
 * @desc Create a new artifact
 * @access Public (for now - can be protected later)
 */
router.post('/', async (req, res) => {
  try {
    const { json_content, session_id, user_id } = req.body;

    // Validate required fields
    if (!json_content) {
      return res.status(400).json({
        success: false,
        message: 'json_content is required'
      });
    }

    // Create artifact
    const artifact = await Artifact.create({
      json_content,
      session_id,
      user_id
    });

    res.status(201).json({
      success: true,
      data: artifact,
      message: 'Artifact created successfully'
    });

  } catch (error) {
    console.error('Error creating artifact:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route GET /api/artifacts
 * @desc Get all artifacts with optional filtering
 * @access Public (for now - can be protected later)
 */
router.get('/', async (req, res) => {
  try {
    const { 
      session_id, 
      user_id, 
      limit = 50, 
      offset = 0,
      recent = false 
    } = req.query;

    let artifacts;

    if (recent === 'true') {
      // Get recent artifacts
      artifacts = await Artifact.findRecent({
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } else if (session_id) {
      // Filter by session
      artifacts = await Artifact.findBySessionId(session_id, {
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } else if (user_id) {
      // Filter by user
      artifacts = await Artifact.findByUserId(parseInt(user_id), {
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } else {
      // Get all artifacts
      artifacts = await Artifact.findAll({
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    }

    res.json({
      success: true,
      data: artifacts,
      count: artifacts.length,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });

  } catch (error) {
    console.error('Error fetching artifacts:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route GET /api/artifacts/:uuid
 * @desc Get a specific artifact by UUID
 * @access Public (for now - can be protected later)
 */
router.get('/:uuid', async (req, res) => {
  try {
    const { uuid } = req.params;
    const { parse_json = false } = req.query;

    let artifact;
    
    if (parse_json === 'true') {
      artifact = await Artifact.getWithParsedJson(uuid);
    } else {
      artifact = await Artifact.findByUuid(uuid);
    }

    if (!artifact) {
      return res.status(404).json({
        success: false,
        message: 'Artifact not found'
      });
    }

    res.json({
      success: true,
      data: artifact
    });

  } catch (error) {
    console.error('Error fetching artifact:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route DELETE /api/artifacts/:uuid
 * @desc Delete a specific artifact
 * @access Public (for now - can be protected later)
 */
router.delete('/:uuid', async (req, res) => {
  try {
    const { uuid } = req.params;

    // Find the artifact first
    const artifact = await Artifact.findByUuid(uuid);
    if (!artifact) {
      return res.status(404).json({
        success: false,
        message: 'Artifact not found'
      });
    }

    // Delete the artifact
    await Artifact.delete(artifact.id);

    res.json({
      success: true,
      message: 'Artifact deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting artifact:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route GET /api/artifacts/session/:sessionId/count
 * @desc Get count of artifacts for a session
 * @access Public (for now - can be protected later)
 */
router.get('/session/:sessionId/count', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const count = await Artifact.countBySession(sessionId);

    res.json({
      success: true,
      data: { count, session_id: sessionId }
    });

  } catch (error) {
    console.error('Error counting artifacts:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route DELETE /api/artifacts/cleanup/:days
 * @desc Delete artifacts older than specified days
 * @access Public (for now - can be protected later)
 */
router.delete('/cleanup/:days', async (req, res) => {
  try {
    const { days } = req.params;
    const daysNum = parseInt(days);

    if (isNaN(daysNum) || daysNum < 1) {
      return res.status(400).json({
        success: false,
        message: 'Days must be a positive number'
      });
    }

    const result = await Artifact.deleteOlderThan(daysNum);

    res.json({
      success: true,
      data: result,
      message: `Deleted ${result.deletedCount} artifacts older than ${daysNum} days`
    });

  } catch (error) {
    console.error('Error cleaning up artifacts:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
