const express = require('express');
const axios = require('axios');
const router = express.Router();
const { authenticate, optionalAuth } = require('../middleware/auth');

// Environment variables for ADK server
const ADK_SERVER_URL = process.env.ADK_SERVER_URL || 'http://127.0.0.1:8000';
const ADK_APP_NAME = process.env.ADK_APP_NAME || 'agency_agent';

// API route for creating a session - requires authentication
router.post('/session', optionalAuth, async (req, res) => {
  try {
    // Get user ID from authenticated session or use guest ID
    const userId = req.user?.email || '<EMAIL>';
    
    const response = await axios.post(
      `${ADK_SERVER_URL}/apps/${ADK_APP_NAME}/users/${userId}/sessions`
    );

    return res.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    console.error('Error creating ADK session:', error.response?.data || error.message);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to create a session with the AI agent',
      details: error.message
    });
  }
});

// API route for sending a message - requires authentication
router.post('', optionalAuth, async (req, res) => {
  try {
    const { message, sessionId } = req.body;

    // Get user ID from authenticated session or use guest ID
    const userId = req.user?.email || '<EMAIL>';

    if (!message || !sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Message and session ID are required'
      });
    }

    // Get the session from our database to find the ADK session ID
    const Session = require('../models/Session');
    const session = await Session.findById(sessionId);

    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }

    if (!session.adk_session_id) {
      console.error('ADK session not available for session:', {
        sessionId: sessionId,
        session: session,
        adkServerUrl: ADK_SERVER_URL,
        adkAppName: ADK_APP_NAME
      });

      return res.status(400).json({
        success: false,
        error: 'ADK session not available for this session. The AI agent server may be unavailable.',
        details: 'Please try creating a new session or contact support if the problem persists.'
      });
    }

    const response = await axios.post(`${ADK_SERVER_URL}/run`, {
      session_id: session.adk_session_id,
      appName: ADK_APP_NAME,
      userId: userId,
      newMessage: {
        parts: [
          {
            text: message
          }
        ],
        role: "user"
      },
      streaming: false
    });
    
    // The response structure is different than expected
    // Extract the assistant's response from the correct location
    let assistantMessage = 'Sorry, I could not generate a response.';
    
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      // Get the last item from the array as it contains the most recent response
      const lastResponse = response.data[response.data.length - 1];
      
      if (lastResponse.content && lastResponse.content.parts && Array.isArray(lastResponse.content.parts)) {
        // Extract text from the first part if available
        assistantMessage = lastResponse.content.parts[0]?.text || assistantMessage;
      }
    } else if (response.data && response.data.events) {
      // Fallback to the events array if the structure is different
      const events = response.data.events || [];
      
      // Get the last event with assistant/model role
      const lastAssistantEvent = events
        .filter(event => 
          (event.content?.role === 'assistant' || event.content?.role === 'model')
        )
        .pop();
      
      assistantMessage = lastAssistantEvent?.content?.parts?.[0]?.text || assistantMessage;
    }
    
    return res.json({
      success: true,
      data: {
        message: assistantMessage,
        sessionId: sessionId,
        events: response.data.events || response.data
      }
    });
  } catch (error) {
    console.error('Agent API error:', error.response?.data || error.message);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to communicate with AI agent',
      details: error.message
    });
  }
});

module.exports = router;
