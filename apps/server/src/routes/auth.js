const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Role = require('../models/Role');
const ActionHistory = require('../models/ActionHistory');
const { generateToken, hashPassword, comparePassword } = require('../utils/auth');
const { authenticate, requirePermission } = require('../middleware/auth');
const { validateRequired, validateEmail, sanitizeInput } = require('../middleware/validation');

// POST /api/auth/register - Register new user
router.post('/register', 
  sanitizeInput,
  validateRequired(['username', 'email', 'password', 'first_name', 'last_name']),
  validateEmail,
  async (req, res, next) => {
    try {
      const { username, email, password, first_name, last_name, role_name = 'operator' } = req.body;

      // Check if username or email already exists
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: 'Email already exists'
        });
      }

      const existingUsername = await User.findByUsername(username);
      if (existingUsername) {
        return res.status(400).json({
          success: false,
          error: 'Username already exists'
        });
      }

      // Validate password strength
      if (password.length < 6) {
        return res.status(400).json({
          success: false,
          error: 'Password must be at least 6 characters long'
        });
      }

      // Get role ID
      const role = await Role.findByName(role_name);
      if (!role) {
        return res.status(400).json({
          success: false,
          error: 'Invalid role specified'
        });
      }

      // Hash password
      const password_hash = await hashPassword(password);

      // Create user
      const userData = {
        username,
        email,
        password_hash,
        first_name,
        last_name,
        role_id: role.id
      };

      const user = await User.create(userData);

      // Get user with role information
      const userWithRole = await User.findWithRole(user.id);

      // Generate token
      const token = generateToken(userWithRole);

      // Log the action
      await ActionHistory.create({
        user_id: user.id,
        action_type: 'create',
        entity_type: 'user',
        entity_id: user.uuid,
        description: `User ${username} registered`,
        metadata: JSON.stringify({ registration: true })
      });

      res.status(201).json({
        success: true,
        data: {
          user: {
            id: userWithRole.id,
            uuid: userWithRole.uuid,
            username: userWithRole.username,
            email: userWithRole.email,
            first_name: userWithRole.first_name,
            last_name: userWithRole.last_name,
            role: {
              id: userWithRole.role_id,
              name: userWithRole.role_name,
              permissions: userWithRole.permissions ? JSON.parse(userWithRole.permissions) : []
            }
          },
          token
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/auth/login - User login
router.post('/login',
  sanitizeInput,
  validateRequired(['email', 'password']),
  validateEmail,
  async (req, res, next) => {
    try {
      const { email, password } = req.body;

      // Find user by email
      const user = await User.findByEmail(email);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        });
      }

      // Check if user is active
      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          error: 'Account is deactivated'
        });
      }

      // Verify password
      const isValidPassword = await comparePassword(password, user.password_hash);
      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        });
      }

      // Get user with role information
      const userWithRole = await User.findWithRole(user.id);

      // Generate token
      const token = generateToken(userWithRole);

      // Log the action
      await ActionHistory.create({
        user_id: user.id,
        action_type: 'login',
        entity_type: 'user',
        entity_id: user.uuid,
        description: `User ${user.username} logged in`,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        data: {
          user: {
            id: userWithRole.id,
            uuid: userWithRole.uuid,
            username: userWithRole.username,
            email: userWithRole.email,
            first_name: userWithRole.first_name,
            last_name: userWithRole.last_name,
            role: {
              id: userWithRole.role_id,
              name: userWithRole.role_name,
              permissions: userWithRole.permissions ? JSON.parse(userWithRole.permissions) : []
            }
          },
          token
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/auth/logout - User logout (optional, mainly for logging)
router.post('/logout', authenticate, async (req, res, next) => {
  try {
    // Log the action
    await ActionHistory.create({
      user_id: req.user.id,
      action_type: 'logout',
      entity_type: 'user',
      entity_id: req.user.uuid,
      description: `User ${req.user.username} logged out`,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/auth/me - Get current user info
router.get('/me', authenticate, async (req, res, next) => {
  try {
    // Get fresh user data
    const user = await User.findWithRole(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        uuid: user.uuid,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        is_active: user.is_active,
        role: {
          id: user.role_id,
          name: user.role_name,
          description: user.role_description,
          permissions: user.permissions ? JSON.parse(user.permissions) : []
        },
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    });
  } catch (error) {
    next(error);
  }
});

// PUT /api/auth/change-password - Change user password
router.put('/change-password',
  authenticate,
  sanitizeInput,
  validateRequired(['current_password', 'new_password']),
  async (req, res, next) => {
    try {
      const { current_password, new_password } = req.body;

      // Get current user
      const user = await User.findById(req.user.id);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Verify current password
      const isValidPassword = await comparePassword(current_password, user.password_hash);
      if (!isValidPassword) {
        return res.status(400).json({
          success: false,
          error: 'Current password is incorrect'
        });
      }

      // Validate new password
      if (new_password.length < 6) {
        return res.status(400).json({
          success: false,
          error: 'New password must be at least 6 characters long'
        });
      }

      // Hash new password
      const new_password_hash = await hashPassword(new_password);

      // Update password
      await User.updatePassword(req.user.id, new_password_hash);

      // Log the action
      await ActionHistory.create({
        user_id: req.user.id,
        action_type: 'update',
        entity_type: 'user',
        entity_id: req.user.uuid,
        description: `User ${req.user.username} changed password`,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        message: 'Password changed successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/auth/roles - Get available roles (for registration)
router.get('/roles', async (req, res, next) => {
  try {
    const roles = await Role.findAll({ 
      where: { is_active: 1 },
      orderBy: 'name'
    });

    // Filter out sensitive roles for public registration
    const publicRoles = roles.filter(role => 
      ['operator', 'editor'].includes(role.name)
    );

    res.json({
      success: true,
      data: publicRoles.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description
      }))
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
