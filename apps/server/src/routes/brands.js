const express = require('express');
const router = express.Router();
const Brand = require('../models/Brand');
const ActionHistory = require('../models/ActionHistory');
const { validateRequired, validateUUID, validatePagination, sanitizeInput } = require('../middleware/validation');
const { authenticate, requirePermission, requireAnyPermission } = require('../middleware/auth');
const { requireBrandScoping, validateBrandAccess } = require('../middleware/brandScoping');

// GET /api/brands/industries - Get all industries
router.get('/industries', async (req, res, next) => {
  try {
    const industries = await Brand.getIndustries();

    res.json({
      success: true,
      data: industries
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/brands - Get all brands (Super Admin only)
router.get('/',
  authenticate,
  requirePermission('manage_brands'),
  validatePagination,
  sanitizeInput,
  async (req, res, next) => {
  try {
    const { page, limit, offset } = req.pagination;
    const { is_active, industry } = req.query;

    const options = {
      limit,
      offset
    };

    if (is_active !== undefined || industry) {
      options.where = {};
      if (is_active !== undefined) options.where.is_active = is_active === 'true' ? 1 : 0;
      if (industry) options.where.industry = industry;
    }

    const brands = await Brand.findAllWithAgentCounts(options);
    const total = await Brand.count(options.where || {});

    res.json({
      success: true,
      data: brands,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/brands/:id/agents - Get agents for a brand
router.get('/:id/agents', async (req, res, next) => {
  try {
    const brand = await Brand.findById(req.params.id);
    if (!brand) {
      return res.status(404).json({
        success: false,
        error: 'Brand not found'
      });
    }

    const agents = await Brand.getAgentsByBrand(req.params.id);

    res.json({
      success: true,
      data: agents
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/brands/:id - Get brand by ID
router.get('/:id', async (req, res, next) => {
  try {
    const brand = await Brand.findWithAgentCount(req.params.id);

    if (!brand) {
      return res.status(404).json({
        success: false,
        error: 'Brand not found'
      });
    }

    res.json({
      success: true,
      data: brand
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/brands - Create new brand (Super Admin only)
router.post('/',
  authenticate,
  requirePermission('manage_brands'),
  sanitizeInput,
  validateRequired(['name']),
  async (req, res, next) => {
    try {
      const { name, description, logo_url, website_url, industry } = req.body;

      // Check if brand name already exists
      const existingBrand = await Brand.findByName(name);
      if (existingBrand) {
        return res.status(400).json({
          success: false,
          error: 'Brand name already exists'
        });
      }

      const brandData = {
        name,
        description,
        logo_url,
        website_url,
        industry
      };

      const brand = await Brand.create(brandData);

      // Log the action
      await ActionHistory.create({
        action_type: 'create',
        entity_type: 'brand',
        entity_id: brand.uuid,
        description: `Brand ${name} created`,
        metadata: JSON.stringify({ brand_id: brand.id })
      });

      res.status(201).json({
        success: true,
        data: brand
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/brands/:id - Update brand (Super Admin only)
router.put('/:id',
  authenticate,
  requirePermission('manage_brands'),
  sanitizeInput,
  async (req, res, next) => {
    try {
      const { name, description, logo_url, website_url, industry, is_active } = req.body;

      // Check if brand exists
      const existingBrand = await Brand.findById(req.params.id);
      if (!existingBrand) {
        return res.status(404).json({
          success: false,
          error: 'Brand not found'
        });
      }

      // Check if name is being changed and if it already exists
      if (name && name !== existingBrand.name) {
        const nameExists = await Brand.findByName(name);
        if (nameExists) {
          return res.status(400).json({
            success: false,
            error: 'Brand name already exists'
          });
        }
      }

      const updateData = {};
      if (name) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (logo_url !== undefined) updateData.logo_url = logo_url;
      if (website_url !== undefined) updateData.website_url = website_url;
      if (industry !== undefined) updateData.industry = industry;
      if (is_active !== undefined) updateData.is_active = is_active;

      const brand = await Brand.update(req.params.id, updateData);

      // Log the action
      await ActionHistory.create({
        action_type: 'update',
        entity_type: 'brand',
        entity_id: brand.uuid,
        description: `Brand ${brand.name} updated`,
        metadata: JSON.stringify({ changes: updateData })
      });

      res.json({
        success: true,
        data: brand
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/brands/:id - Delete brand (Super Admin only)
router.delete('/:id',
  authenticate,
  requirePermission('manage_brands'),
  async (req, res, next) => {
  try {
    const brand = await Brand.findById(req.params.id);
    if (!brand) {
      return res.status(404).json({
        success: false,
        error: 'Brand not found'
      });
    }

    // Check if brand has agents
    const agents = await Brand.getAgentsByBrand(req.params.id);
    if (agents.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete brand with existing agents'
      });
    }

    await Brand.delete(req.params.id);

    // Log the action
    await ActionHistory.create({
      action_type: 'delete',
      entity_type: 'brand',
      entity_id: brand.uuid,
      description: `Brand ${brand.name} deleted`
    });

    res.json({
      success: true,
      message: 'Brand deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/brands/:id/users - Get users for a brand
router.get('/:id/users',
  authenticate,
  requireAnyPermission(['manage_brands', 'manage_brand_users']),
  validateBrandAccess,
  async (req, res, next) => {
    try {
      const User = require('../models/User');
      const brand = await Brand.findById(req.params.id);

      if (!brand) {
        return res.status(404).json({
          success: false,
          error: 'Brand not found'
        });
      }

      const users = await User.findByBrand(req.params.id);

      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/brands/:id/users - Assign user to brand
router.post('/:id/users',
  authenticate,
  requireAnyPermission(['manage_brands', 'manage_brand_users']),
  validateBrandAccess,
  sanitizeInput,
  validateRequired(['user_id']),
  async (req, res, next) => {
    try {
      const User = require('../models/User');
      const { user_id } = req.body;

      const brand = await Brand.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({
          success: false,
          error: 'Brand not found'
        });
      }

      const user = await User.findById(user_id);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Update user's brand assignment
      await User.update(user_id, { brand_id: req.params.id });

      // Log the action
      await ActionHistory.create({
        user_id: req.user.id,
        action_type: 'update',
        entity_type: 'user',
        entity_id: user.uuid,
        description: `User ${user.username} assigned to brand ${brand.name}`,
        metadata: JSON.stringify({ brand_id: brand.id, user_id: user.id })
      });

      res.json({
        success: true,
        message: 'User assigned to brand successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;
