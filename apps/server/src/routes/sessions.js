const express = require('express');
const router = express.Router();
const Session = require('../models/Session');
const User = require('../models/User');
const ActionHistory = require('../models/ActionHistory');
const { authenticate, optionalAuth } = require('../middleware/auth');

// POST /api/sessions - Create a new session
router.post('/', optionalAuth, async (req, res, next) => {
  try {
    // Get user ID from authenticated session or use guest ID
    const userId = req.user?.id || 'guest';
    const userEmail = req.user?.email || null;

    const session = await Session.create(userId, userEmail);
    
    // Log the action if authenticated
    if (req.user) {
      await ActionHistory.create({
        user_id: req.user.id,
        action_type: 'create',
        entity_type: 'session',
        entity_id: session.id,
        description: `Session created by ${req.user.username}`,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });
    }

    return res.json({
      success: true,
      data: session
    });
  } catch (error) {
    console.error('Error creating session:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to create a session',
      details: error.message
    });
  }
});

// GET /api/sessions/:id - Get session details
router.get('/:id', optionalAuth, async (req, res, next) => {
  try {
    const sessionId = req.params.id;
    
    // Check if session exists
    const session = await Session.findById(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    // Get participants
    const participants = await Session.getParticipants(sessionId);
    
    // If user is authenticated, update their activity status
    if (req.user) {
      await Session.updateParticipantActivity(sessionId, req.user.id, true);
    }
    
    return res.json({
      success: true,
      data: {
        session,
        participants
      }
    });
  } catch (error) {
    console.error('Error getting session:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to get session details',
      details: error.message
    });
  }
});

// POST /api/sessions/:id/participants - Add participant to session
router.post('/:id/participants', authenticate, async (req, res, next) => {
  try {
    const sessionId = req.params.id;
    const { userId, accessType } = req.body;
    
    if (!userId || !['view', 'edit'].includes(accessType)) {
      return res.status(400).json({
        success: false,
        error: 'User ID and valid access type (view/edit) are required'
      });
    }
    
    // Check if session exists
    const session = await Session.findById(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Add participant
    await Session.addParticipant(sessionId, userId, accessType);
    
    // Get updated participants list
    const participants = await Session.getParticipants(sessionId);
    
    // Log the action
    await ActionHistory.create({
      user_id: req.user.id,
      action_type: 'share',
      entity_type: 'session',
      entity_id: sessionId,
      description: `Session shared with user ${user.username} (${accessType} access)`,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });
    
    return res.json({
      success: true,
      data: {
        participants
      }
    });
  } catch (error) {
    console.error('Error adding participant:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to add participant to session',
      details: error.message
    });
  }
});

// PUT /api/sessions/:id/activity - Update user activity in session
router.put('/:id/activity', optionalAuth, async (req, res, next) => {
  try {
    const sessionId = req.params.id;
    const { isActive = true } = req.body;
    
    // If not authenticated, can't update activity
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }
    
    // Check if session exists
    const session = await Session.findById(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    // Update activity
    await Session.updateParticipantActivity(sessionId, req.user.id, isActive);
    
    // Get updated participants list
    const participants = await Session.getParticipants(sessionId);
    
    return res.json({
      success: true,
      data: {
        participants
      }
    });
  } catch (error) {
    console.error('Error updating activity:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to update activity status',
      details: error.message
    });
  }
});

// DELETE /api/sessions/:id - Delete a session
router.delete('/:id', authenticate, async (req, res, next) => {
  try {
    const sessionId = req.params.id;
    
    // Check if session exists
    const session = await Session.findById(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    // Delete session
    await Session.delete(sessionId);
    
    // Log the action
    await ActionHistory.create({
      user_id: req.user.id,
      action_type: 'delete',
      entity_type: 'session',
      entity_id: sessionId,
      description: `Session deleted by ${req.user.username}`,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });
    
    return res.json({
      success: true,
      message: 'Session deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting session:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to delete session',
      details: error.message
    });
  }
});

module.exports = router;