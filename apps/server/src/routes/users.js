const express = require('express');
const router = express.Router();
const User = require('../models/User');
const ActionHistory = require('../models/ActionHistory');
const { validateRequired, validateEmail, validateUUID, validatePagination, sanitizeInput } = require('../middleware/validation');
const { authenticate, requirePermission, requireAnyPermission, requireOwnershipOrAdmin } = require('../middleware/auth');
const { requireBrandUserAccess } = require('../middleware/brandScoping');
const { hashPassword } = require('../utils/auth');

// GET /api/users - Get all users (requires user management permission)
router.get('/',
  authenticate,
  requireBrandUserAccess,
  validatePagination,
  sanitizeInput,
  async (req, res, next) => {
  try {
    const { page, limit, offset } = req.pagination;
    const { is_active, role_id } = req.query;

    const options = {
      limit,
      offset
    };

    if (is_active !== undefined || role_id) {
      options.where = {};
      if (is_active !== undefined) options.where.is_active = is_active === 'true' ? 1 : 0;
      if (role_id) options.where.role_id = role_id;
    }

    let users;
    let total;

    // If brand restriction is set (Brand Admin), only show users from their brand
    if (req.brandUserRestriction) {
      if (!options.where) options.where = {};
      options.where.brand_id = req.brandUserRestriction;
      users = await User.findAllWithRoles(options);
      total = await User.count(options.where);
    } else {
      // Super Admin can see all users
      users = await User.findAllWithRoles(options);
      total = await User.count(options.where || {});
    }

    res.json({
      success: true,
      data: users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/users/for-sharing - Get users for session sharing with search (requires authentication only)
router.get('/for-sharing',
  authenticate,
  async (req, res, next) => {
  try {
    if (!req.user.brand_id) {
      return res.status(403).json({
        success: false,
        error: 'User not assigned to any brand'
      });
    }

    const { search } = req.query;

    // Get all active users from the same brand
    const users = await User.findByBrand(req.user.brand_id);

    // Filter out the current user and only return necessary fields
    let filteredUsers = users
      .filter(user => user.id !== req.user.id)
      .map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role_name: user.role_name,
        display_name: `${user.first_name} ${user.last_name}`.trim() || user.username
      }));

    // Apply search filter if search query is provided
    if (search && search.trim()) {
      const searchTerm = search.trim().toLowerCase();
      filteredUsers = filteredUsers.filter(user => {
        const fullName = `${user.first_name} ${user.last_name}`.toLowerCase();
        const username = user.username.toLowerCase();
        const email = user.email.toLowerCase();
        const roleName = user.role_name.toLowerCase();

        return fullName.includes(searchTerm) ||
               username.includes(searchTerm) ||
               email.includes(searchTerm) ||
               roleName.includes(searchTerm);
      });
    }

    res.json({
      success: true,
      data: filteredUsers
    });
  } catch (error) {
    console.error('Error in for-sharing endpoint:', error);
    next(error);
  }
});

// GET /api/users/:id - Get user by ID (requires ownership or admin)
router.get('/:id',
  authenticate,
  requireOwnershipOrAdmin,
  async (req, res, next) => {
  try {
    const user = await User.findWithRole(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/users - Create new user (requires admin permission)
router.post('/',
  authenticate,
  requireBrandUserAccess,
  sanitizeInput,
  validateRequired(['username', 'email', 'password', 'first_name', 'last_name']),
  validateEmail,
  async (req, res, next) => {
    try {
      const { username, email, password, first_name, last_name, role_id, brand_id } = req.body;

      // Check if username or email already exists
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: 'Email already exists'
        });
      }

      const existingUsername = await User.findByUsername(username);
      if (existingUsername) {
        return res.status(400).json({
          success: false,
          error: 'Username already exists'
        });
      }

      // Hash password
      const password_hash = await hashPassword(password);

      // Determine brand assignment
      let assignedBrandId = brand_id;

      // If brand restriction is set (Brand Admin), assign to their brand
      if (req.brandUserRestriction) {
        assignedBrandId = req.brandUserRestriction;
      }

      const userData = {
        username,
        email,
        password_hash,
        first_name,
        last_name,
        role_id,
        brand_id: assignedBrandId
      };

      const user = await User.create(userData);

      // Log the action
      await ActionHistory.create({
        action_type: 'create',
        entity_type: 'user',
        entity_id: user.uuid,
        description: `User ${username} created`,
        metadata: JSON.stringify({ user_id: user.id })
      });

      res.status(201).json({
        success: true,
        data: user
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/users/:id - Update user (requires ownership or admin)
router.put('/:id',
  authenticate,
  requireOwnershipOrAdmin,
  sanitizeInput,
  validateEmail,
  async (req, res, next) => {
    try {
      const { username, email, first_name, last_name, role_id, brand_id, is_active } = req.body;

      // Check if user exists
      const existingUser = await User.findById(req.params.id);
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Additional permission check for role changes
      // Only super admins can change roles
      if (role_id && role_id !== existingUser.role_id) {
        const hasManageUsersPermission = req.user.role.permissions.includes('manage_users');
        if (!hasManageUsersPermission) {
          return res.status(403).json({
            success: false,
            error: 'You do not have permission to change user roles'
          });
        }
      }

      // Additional permission check for brand changes
      // Only super admins can change brands
      if (brand_id && brand_id !== existingUser.brand_id) {
        const hasManageUsersPermission = req.user.role.permissions.includes('manage_users');
        if (!hasManageUsersPermission) {
          return res.status(403).json({
            success: false,
            error: 'You do not have permission to change user brands'
          });
        }
      }

      // Check if email is being changed and if it already exists
      if (email && email !== existingUser.email) {
        const emailExists = await User.findByEmail(email);
        if (emailExists) {
          return res.status(400).json({
            success: false,
            error: 'Email already exists'
          });
        }
      }

      // Check if username is being changed and if it already exists
      if (username && username !== existingUser.username) {
        const usernameExists = await User.findByUsername(username);
        if (usernameExists) {
          return res.status(400).json({
            success: false,
            error: 'Username already exists'
          });
        }
      }

      const updateData = {};
      if (username) updateData.username = username;
      if (email) updateData.email = email;
      if (first_name !== undefined) updateData.first_name = first_name;
      if (last_name !== undefined) updateData.last_name = last_name;
      if (role_id) updateData.role_id = role_id;
      if (brand_id !== undefined) updateData.brand_id = brand_id;
      if (is_active !== undefined) updateData.is_active = is_active;

      // Log what we're updating for debugging
      console.log('Updating user:', req.params.id, 'with data:', updateData);

      const user = await User.update(req.params.id, updateData);

      // Log the action
      await ActionHistory.create({
        action_type: 'update',
        entity_type: 'user',
        entity_id: user.uuid,
        description: `User ${user.username} updated`,
        metadata: JSON.stringify({ changes: updateData })
      });

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('Error updating user:', error);
      next(error);
    }
  }
);

// DELETE /api/users/:id - Delete user (requires admin permission)
router.delete('/:id',
  authenticate,
  requireAnyPermission(['manage_users', 'manage_brand_users']),
  async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    await User.delete(req.params.id);

    // Log the action
    await ActionHistory.create({
      action_type: 'delete',
      entity_type: 'user',
      entity_id: user.uuid,
      description: `User ${user.username} deleted`
    });

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/users/:id/deactivate - Deactivate user (requires admin permission)
router.post('/:id/deactivate',
  authenticate,
  requireAnyPermission(['manage_users', 'manage_brand_users']),
  async (req, res, next) => {
  try {
    const user = await User.deactivate(req.params.id);

    // Log the action
    await ActionHistory.create({
      action_type: 'update',
      entity_type: 'user',
      entity_id: user.uuid,
      description: `User ${user.username} deactivated`
    });

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/users/:id/activate - Activate user (requires admin permission)
router.post('/:id/activate',
  authenticate,
  requireAnyPermission(['manage_users', 'manage_brand_users']),
  async (req, res, next) => {
  try {
    const user = await User.activate(req.params.id);

    // Log the action
    await ActionHistory.create({
      action_type: 'update',
      entity_type: 'user',
      entity_id: user.uuid,
      description: `User ${user.username} activated`
    });

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
