const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const database = require('./connection');

async function initializeDatabase() {
  try {
    console.log('Initializing database...');
    
    // Connect to database
    await database.connect();
    
    // Read and execute schema
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Split schema into individual statements
    const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      await database.run(statement.trim());
    }
    
    console.log('Database schema created successfully.');
    
    // Insert default data
    await insertDefaultData();
    
    console.log('Database initialization completed.');
    
  } catch (error) {
    console.error('Error initializing database:', error);
    process.exit(1);
  } finally {
    await database.close();
  }
}

async function insertDefaultData() {
  console.log('Inserting default data...');

  try {
    // Insert default roles with proper hierarchy
    const superAdminRoleUuid = uuidv4();
    const brandAdminRoleUuid = uuidv4();
    const editorRoleUuid = uuidv4();
    const operatorRoleUuid = uuidv4();

    // Super Admin - Full system access
    await database.run(`
      INSERT OR IGNORE INTO Roles (uuid, name, description, permissions)
      VALUES (?, ?, ?, ?)
    `, [
      superAdminRoleUuid,
      'super_admin',
      'Super Administrator - Full system access',
      JSON.stringify([
        'create', 'read', 'update', 'delete',
        'manage_users', 'manage_brands', 'manage_agents', 'manage_roles',
        'view_all_data', 'system_settings'
      ])
    ]);

    // Brand Admin - Manage specific brand
    await database.run(`
      INSERT OR IGNORE INTO Roles (uuid, name, description, permissions)
      VALUES (?, ?, ?, ?)
    `, [
      brandAdminRoleUuid,
      'brand_admin',
      'Brand Administrator - Manage brand workspace',
      JSON.stringify([
        'create', 'read', 'update', 'delete',
        'manage_brand_users', 'manage_brand_agents',
        'view_brand_data', 'brand_settings'
      ])
    ]);

    // Editor - Content creation and editing
    await database.run(`
      INSERT OR IGNORE INTO Roles (uuid, name, description, permissions)
      VALUES (?, ?, ?, ?)
    `, [
      editorRoleUuid,
      'editor',
      'Editor - Create and edit content',
      JSON.stringify([
        'create', 'read', 'update',
        'create_agents', 'edit_agents', 'create_outputs',
        'view_brand_data'
      ])
    ]);

    // Operator - Basic operations
    await database.run(`
      INSERT OR IGNORE INTO Roles (uuid, name, description, permissions)
      VALUES (?, ?, ?, ?)
    `, [
      operatorRoleUuid,
      'operator',
      'Operator - Basic operations and monitoring',
      JSON.stringify([
        'read', 'create_outputs', 'view_brand_data'
      ])
    ]);
    
    // Insert default brands
    const defaultBrandUuid = uuidv4();
    const cerefluxBrandUuid = uuidv4();
    const fashionBrandUuid = uuidv4();
    
    await database.run(`
      INSERT OR IGNORE INTO Brands (uuid, name, description, industry) 
      VALUES (?, ?, ?, ?)
    `, [
      defaultBrandUuid,
      'Default Brand',
      'Default brand for the AI Agency platform',
      'Technology'
    ]);
    
    await database.run(`
      INSERT OR IGNORE INTO Brands (uuid, name, description, industry) 
      VALUES (?, ?, ?, ?)
    `, [
      cerefluxBrandUuid,
      'Cereflux Therapeutics',
      'A pharma company in the migraine and headache space',
      'Pharmaceutical'
    ]);
    
    await database.run(`
      INSERT OR IGNORE INTO Brands (uuid, name, description, industry) 
      VALUES (?, ?, ?, ?)
    `, [
      fashionBrandUuid,
      'StyleSphere',
      'Contemporary fashion and lifestyle brand',
      'Fashion'
    ]);
    
    // Get role and brand IDs from database
    const superAdminRole = await database.get('SELECT id FROM Roles WHERE name = ?', ['super_admin']);
    const brandAdminRole = await database.get('SELECT id FROM Roles WHERE name = ?', ['brand_admin']);
    const editorRole = await database.get('SELECT id FROM Roles WHERE name = ?', ['editor']);
    const operatorRole = await database.get('SELECT id FROM Roles WHERE name = ?', ['operator']);
    
    const defaultBrand = await database.get('SELECT id FROM Brands WHERE name = ?', ['Default Brand']);
    const cerefluxBrand = await database.get('SELECT id FROM Brands WHERE name = ?', ['Cereflux Therapeutics']);
    const fashionBrand = await database.get('SELECT id FROM Brands WHERE name = ?', ['StyleSphere']);
    
    // Hash passwords for all users
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('intouch-usr', 12);
    const hashedUserPassword = await bcrypt.hash('password123', 12);
    
    // Insert default admin user
    const adminUserUuid = uuidv4();
    await database.run(`
      INSERT OR IGNORE INTO Users (uuid, username, email, password_hash, first_name, last_name, role_id, brand_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      adminUserUuid,
      'admin',
      '<EMAIL>',
      hashedPassword,
      'System',
      'Administrator',
      superAdminRole.id,
      defaultBrand.id
    ]);
    
    // Insert additional test users with different roles
    
    // Brand Admin for Cereflux Therapeutics
    const cerefluxAdminUuid = uuidv4();
    await database.run(`
      INSERT OR IGNORE INTO Users (uuid, username, email, password_hash, first_name, last_name, role_id, brand_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      cerefluxAdminUuid,
      'cerefluxadmin',
      '<EMAIL>',
      hashedUserPassword,
      'Alex',
      'Morgan',
      brandAdminRole.id,
      cerefluxBrand.id
    ]);
    
    // Brand Admin for StyleSphere
    const fashionAdminUuid = uuidv4();
    await database.run(`
      INSERT OR IGNORE INTO Users (uuid, username, email, password_hash, first_name, last_name, role_id, brand_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      fashionAdminUuid,
      'fashionadmin',
      '<EMAIL>',
      hashedUserPassword,
      'Jordan',
      'Taylor',
      brandAdminRole.id,
      fashionBrand.id
    ]);
    
    // Editor for Cereflux Therapeutics
    const cerefluxEditorUuid = uuidv4();
    await database.run(`
      INSERT OR IGNORE INTO Users (uuid, username, email, password_hash, first_name, last_name, role_id, brand_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      cerefluxEditorUuid,
      'cerefluxeditor',
      '<EMAIL>',
      hashedUserPassword,
      'Sam',
      'Rivera',
      editorRole.id,
      cerefluxBrand.id
    ]);
    
    // Editor for StyleSphere
    const fashionEditorUuid = uuidv4();
    await database.run(`
      INSERT OR IGNORE INTO Users (uuid, username, email, password_hash, first_name, last_name, role_id, brand_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      fashionEditorUuid,
      'fashioneditor',
      '<EMAIL>',
      hashedUserPassword,
      'Casey',
      'Johnson',
      editorRole.id,
      fashionBrand.id
    ]);
    
    // Operator for Cereflux Therapeutics
    const cerefluxOperatorUuid = uuidv4();
    await database.run(`
      INSERT OR IGNORE INTO Users (uuid, username, email, password_hash, first_name, last_name, role_id, brand_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      cerefluxOperatorUuid,
      'cerefluxoperator',
      '<EMAIL>',
      hashedUserPassword,
      'Riley',
      'Chen',
      operatorRole.id,
      cerefluxBrand.id
    ]);
    
    // Operator for StyleSphere
    const fashionOperatorUuid = uuidv4();
    await database.run(`
      INSERT OR IGNORE INTO Users (uuid, username, email, password_hash, first_name, last_name, role_id, brand_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      fashionOperatorUuid,
      'fashionoperator',
      '<EMAIL>',
      hashedUserPassword,
      'Taylor',
      'Smith',
      operatorRole.id,
      fashionBrand.id
    ]);
    
    console.log('Default data inserted successfully.');
    
  } catch (error) {
    console.error('Error inserting default data:', error);
    throw error;
  }
}

// Run initialization if this file is executed directly
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase, insertDefaultData };
