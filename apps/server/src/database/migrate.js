const database = require('./connection');

async function migrateDatabase() {
  try {
    console.log('Starting database migration...');
    
    // Connect to database
    await database.connect();
    
    // Check if brand_id column exists in Users table
    const tableInfo = await database.all("PRAGMA table_info(Users)");
    const hasBrandId = tableInfo.some(column => column.name === 'brand_id');
    
    if (!hasBrandId) {
      console.log('Adding brand_id column to Users table...');
      
      // Add brand_id column to Users table
      await database.run(`
        ALTER TABLE Users 
        ADD COLUMN brand_id INTEGER 
        REFERENCES Brands(id)
      `);
      
      // Create index for brand_id
      await database.run(`
        CREATE INDEX IF NOT EXISTS idx_users_brand_id ON Users(brand_id)
      `);
      
      // Get the default brand ID
      const defaultBrand = await database.get('SELECT id FROM Brands WHERE name = ?', ['Default Brand']);
      
      if (defaultBrand) {
        // Update existing users to be assigned to the default brand
        await database.run(`
          UPDATE Users 
          SET brand_id = ? 
          WHERE brand_id IS NULL
        `, [defaultBrand.id]);
        
        console.log('Assigned existing users to default brand');
      }
      
      console.log('Migration completed successfully');
    } else {
      console.log('brand_id column already exists, skipping migration');
    }
    
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    await database.close();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateDatabase()
    .then(() => {
      console.log('Migration finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateDatabase };
