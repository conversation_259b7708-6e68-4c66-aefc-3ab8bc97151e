const sqlite3 = require('sqlite3').verbose();
const path = require('path');
require('dotenv').config();

class Database {
  constructor() {
    this.db = null;
  }

  connect() {
    return new Promise((resolve, reject) => {
      const dbPath = process.env.DB_PATH || './database/agency.db';
      const fullPath = path.resolve(dbPath);

      // Ensure database directory exists
      const fs = require('fs');
      const dbDir = path.dirname(fullPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      this.db = new sqlite3.Database(fullPath, async (err) => {
        if (err) {
          console.error('Error opening database:', err.message);
          reject(err);
        } else {
          console.log('Connected to SQLite database:', fullPath);
          // Enable foreign keys
          this.db.run('PRAGMA foreign_keys = ON');

          // Ensure sessions tables exist
          try {
            await this.ensureSessionsTablesExist();
            resolve(this.db);
          } catch (tableError) {
            console.error('Error creating sessions tables:', tableError.message);
            reject(tableError);
          }
        }
      });
    });
  }

  async ensureSessionsTablesExist() {
    const sessionsTableSQL = `
      CREATE TABLE IF NOT EXISTS sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        adk_session_id TEXT,
        created_by TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const sessionParticipantsTableSQL = `
      CREATE TABLE IF NOT EXISTS session_participants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        session_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        access_type TEXT NOT NULL DEFAULT 'view',
        is_active BOOLEAN DEFAULT 1,
        last_active DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES sessions(uuid) ON DELETE CASCADE
      )
    `;

    try {
      await this.run(sessionsTableSQL);
      await this.run(sessionParticipantsTableSQL);
      console.log('Sessions tables ensured to exist.');
    } catch (error) {
      console.error('Error creating sessions tables:', error);
      throw error;
    }
  }

  close() {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err.message);
            reject(err);
          } else {
            console.log('Database connection closed.');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}

// Create singleton instance
const database = new Database();

module.exports = database;
