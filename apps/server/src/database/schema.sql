-- Users table
CREATE TABLE IF NOT EXISTS Users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    first_name TEXT,
    last_name TEXT,
    role_id INTEGER,
    brand_id INTEGER,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES Roles(id),
    FOREIGN KEY (brand_id) REFERENCES Brands(id)
);

-- Roles table
CREATE TABLE IF NOT EXISTS Roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    permissions TEXT, -- <PERSON><PERSON><PERSON> string of permissions
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMES<PERSON>MP
);

-- Brands table
CREATE TABLE IF NOT EXISTS Brands (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    logo_url TEXT,
    website_url TEXT,
    industry TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Agents table
CREATE TABLE IF NOT EXISTS Agents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL, -- e.g., 'chatbot', 'voice', 'task_automation'
    brand_id INTEGER,
    configuration TEXT, -- JSON string of agent configuration
    model_settings TEXT, -- JSON string of AI model settings
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES Brands(id)
);

-- Agent_Outputs table
CREATE TABLE IF NOT EXISTS Agent_Outputs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    agent_id INTEGER NOT NULL,
    user_id INTEGER,
    session_id TEXT,
    input_text TEXT,
    output_text TEXT,
    output_type TEXT, -- e.g., 'text', 'image', 'audio', 'action'
    metadata TEXT, -- JSON string of additional metadata
    processing_time_ms INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES Agents(id),
    FOREIGN KEY (user_id) REFERENCES Users(id)
);

-- Action_History table
CREATE TABLE IF NOT EXISTS Action_History (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    user_id INTEGER,
    agent_id INTEGER,
    action_type TEXT NOT NULL, -- e.g., 'create', 'update', 'delete', 'execute'
    entity_type TEXT NOT NULL, -- e.g., 'user', 'agent', 'brand'
    entity_id TEXT,
    description TEXT,
    metadata TEXT, -- JSON string of action details
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(id),
    FOREIGN KEY (agent_id) REFERENCES Agents(id)
);

-- Logs table
CREATE TABLE IF NOT EXISTS Logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    level TEXT NOT NULL, -- e.g., 'info', 'warn', 'error', 'debug'
    message TEXT NOT NULL,
    source TEXT, -- e.g., 'api', 'agent', 'database'
    user_id INTEGER,
    agent_id INTEGER,
    metadata TEXT, -- JSON string of additional log data
    stack_trace TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(id),
    FOREIGN KEY (agent_id) REFERENCES Agents(id)
);

-- Artifacts table
CREATE TABLE IF NOT EXISTS Artifacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    session_id TEXT,
    user_id INTEGER,
    json_content TEXT NOT NULL, -- Raw JSON response from AI Agent
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(uuid) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE SET NULL
);

-- Sessions table
CREATE TABLE IF NOT EXISTS sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    adk_session_id TEXT, -- ADK server session ID
    created_by TEXT NOT NULL, -- Can be user ID or 'guest'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Session participants table
CREATE TABLE IF NOT EXISTS session_participants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uuid TEXT UNIQUE NOT NULL,
    session_id TEXT NOT NULL,
    user_id TEXT NOT NULL, -- Can be user ID or 'guest'
    access_type TEXT NOT NULL DEFAULT 'view', -- 'view' or 'edit'
    is_active BOOLEAN DEFAULT 1,
    last_active DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(uuid) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON Users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON Users(username);
CREATE INDEX IF NOT EXISTS idx_users_uuid ON Users(uuid);
CREATE INDEX IF NOT EXISTS idx_users_brand_id ON Users(brand_id);
CREATE INDEX IF NOT EXISTS idx_roles_name ON Roles(name);
CREATE INDEX IF NOT EXISTS idx_brands_name ON Brands(name);
CREATE INDEX IF NOT EXISTS idx_agents_brand_id ON Agents(brand_id);
CREATE INDEX IF NOT EXISTS idx_agent_outputs_agent_id ON Agent_Outputs(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_outputs_user_id ON Agent_Outputs(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_outputs_session_id ON Agent_Outputs(session_id);
CREATE INDEX IF NOT EXISTS idx_artifacts_session_id ON Artifacts(session_id);
CREATE INDEX IF NOT EXISTS idx_artifacts_user_id ON Artifacts(user_id);
CREATE INDEX IF NOT EXISTS idx_artifacts_created_at ON Artifacts(created_at);
CREATE INDEX IF NOT EXISTS idx_action_history_user_id ON Action_History(user_id);
CREATE INDEX IF NOT EXISTS idx_action_history_agent_id ON Action_History(agent_id);
CREATE INDEX IF NOT EXISTS idx_logs_level ON Logs(level);
CREATE INDEX IF NOT EXISTS idx_logs_source ON Logs(source);
CREATE INDEX IF NOT EXISTS idx_logs_created_at ON Logs(created_at);
