const BaseModel = require('./BaseModel');
const database = require('../database/connection');

class Agent extends BaseModel {
  constructor() {
    super('Agents');
  }

  async findByName(name) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE name = ?`;
      return await database.get(sql, [name]);
    } catch (error) {
      throw new Error(`Error finding agent by name: ${error.message}`);
    }
  }

  async findWithBrand(id) {
    try {
      const sql = `
        SELECT a.*, b.name as brand_name, b.description as brand_description
        FROM ${this.tableName} a
        LEFT JOIN Brands b ON a.brand_id = b.id
        WHERE a.id = ?
      `;
      return await database.get(sql, [id]);
    } catch (error) {
      throw new Error(`Error finding agent with brand: ${error.message}`);
    }
  }

  async findAllWithBrands(options = {}) {
    try {
      let sql = `
        SELECT a.*, b.name as brand_name, b.description as brand_description
        FROM ${this.tableName} a
        LEFT JOIN Brands b ON a.brand_id = b.id
      `;
      const params = [];

      // Add WHERE clause if conditions provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `a.${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY a.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY a.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding agents with brands: ${error.message}`);
    }
  }

  async findByType(type) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE type = ? ORDER BY name`;
      return await database.all(sql, [type]);
    } catch (error) {
      throw new Error(`Error finding agents by type: ${error.message}`);
    }
  }

  async findByBrand(brandId) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE brand_id = ? ORDER BY name`;
      return await database.all(sql, [brandId]);
    } catch (error) {
      throw new Error(`Error finding agents by brand: ${error.message}`);
    }
  }

  async updateConfiguration(id, configuration) {
    try {
      const configJson = JSON.stringify(configuration);
      const sql = `UPDATE ${this.tableName} SET configuration = ?, updated_at = ? WHERE id = ?`;
      const result = await database.run(sql, [configJson, new Date().toISOString(), id]);

      if (result.changes === 0) {
        throw new Error(`Agent with ID ${id} not found`);
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Error updating agent configuration: ${error.message}`);
    }
  }

  async updateModelSettings(id, modelSettings) {
    try {
      const settingsJson = JSON.stringify(modelSettings);
      const sql = `UPDATE ${this.tableName} SET model_settings = ?, updated_at = ? WHERE id = ?`;
      const result = await database.run(sql, [settingsJson, new Date().toISOString(), id]);

      if (result.changes === 0) {
        throw new Error(`Agent with ID ${id} not found`);
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Error updating agent model settings: ${error.message}`);
    }
  }

  async getOutputs(agentId, options = {}) {
    try {
      let sql = `
        SELECT ao.*, u.username, u.email
        FROM Agent_Outputs ao
        LEFT JOIN Users u ON ao.user_id = u.id
        WHERE ao.agent_id = ?
      `;
      const params = [agentId];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ao.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ao.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error getting agent outputs: ${error.message}`);
    }
  }

  async getTypes() {
    try {
      const sql = `SELECT DISTINCT type FROM ${this.tableName} WHERE type IS NOT NULL ORDER BY type`;
      const results = await database.all(sql);
      return results.map(row => row.type);
    } catch (error) {
      throw new Error(`Error getting agent types: ${error.message}`);
    }
  }
}

module.exports = new Agent();
