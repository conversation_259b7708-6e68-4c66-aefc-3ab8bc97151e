const BaseModel = require('./BaseModel');
const database = require('../database/connection');

class Brand extends BaseModel {
  constructor() {
    super('Brands');
  }

  async findByName(name) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE name = ?`;
      return await database.get(sql, [name]);
    } catch (error) {
      throw new Error(`Error finding brand by name: ${error.message}`);
    }
  }

  async findWithAgentCount(id) {
    try {
      const sql = `
        SELECT b.*, COUNT(a.id) as agent_count
        FROM ${this.tableName} b
        LEFT JOIN Agents a ON b.id = a.brand_id
        WHERE b.id = ?
        GROUP BY b.id
      `;
      return await database.get(sql, [id]);
    } catch (error) {
      throw new Error(`Error finding brand with agent count: ${error.message}`);
    }
  }

  async findAllWithAgentCounts(options = {}) {
    try {
      let sql = `
        SELECT b.*, COUNT(a.id) as agent_count
        FROM ${this.tableName} b
        LEFT JOIN Agents a ON b.id = a.brand_id
      `;
      const params = [];

      // Add WHERE clause if conditions provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `b.${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      sql += ` GROUP BY b.id`;

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY b.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY b.created_at DESC`;
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding brands with agent counts: ${error.message}`);
    }
  }

  async getAgentsByBrand(brandId) {
    try {
      const sql = `
        SELECT a.id, a.uuid, a.name, a.description, a.type, a.is_active, a.created_at
        FROM Agents a
        WHERE a.brand_id = ?
        ORDER BY a.created_at DESC
      `;
      return await database.all(sql, [brandId]);
    } catch (error) {
      throw new Error(`Error finding agents by brand: ${error.message}`);
    }
  }

  async findByIndustry(industry) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE industry = ? ORDER BY name`;
      return await database.all(sql, [industry]);
    } catch (error) {
      throw new Error(`Error finding brands by industry: ${error.message}`);
    }
  }

  async getIndustries() {
    try {
      const sql = `SELECT DISTINCT industry FROM ${this.tableName} WHERE industry IS NOT NULL ORDER BY industry`;
      const results = await database.all(sql);
      return results.map(row => row.industry);
    } catch (error) {
      throw new Error(`Error getting industries: ${error.message}`);
    }
  }
}

module.exports = new Brand();
