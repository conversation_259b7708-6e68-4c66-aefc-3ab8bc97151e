const BaseModel = require('./BaseModel');
const database = require('../database/connection');

class ActionHistory extends BaseModel {
  constructor() {
    super('Action_History');
  }

  async findWithDetails(id) {
    try {
      const sql = `
        SELECT ah.*, 
               u.username, u.email,
               a.name as agent_name
        FROM ${this.tableName} ah
        LEFT JOIN Users u ON ah.user_id = u.id
        LEFT JOIN Agents a ON ah.agent_id = a.id
        WHERE ah.id = ?
      `;
      return await database.get(sql, [id]);
    } catch (error) {
      throw new Error(`Error finding action history with details: ${error.message}`);
    }
  }

  async findAllWithDetails(options = {}) {
    try {
      let sql = `
        SELECT ah.*, 
               u.username, u.email,
               a.name as agent_name
        FROM ${this.tableName} ah
        LEFT JOIN Users u ON ah.user_id = u.id
        LEFT JOIN Agents a ON ah.agent_id = a.id
      `;
      const params = [];

      // Add WHERE clause if conditions provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `ah.${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ah.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ah.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      // Add OFFSET clause
      if (options.offset) {
        sql += ` OFFSET ?`;
        params.push(options.offset);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding action history with details: ${error.message}`);
    }
  }

  async findByUser(userId, options = {}) {
    try {
      let sql = `
        SELECT ah.*, a.name as agent_name
        FROM ${this.tableName} ah
        LEFT JOIN Agents a ON ah.agent_id = a.id
        WHERE ah.user_id = ?
      `;
      const params = [userId];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ah.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ah.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding action history by user: ${error.message}`);
    }
  }

  async findByAgent(agentId, options = {}) {
    try {
      let sql = `
        SELECT ah.*, u.username, u.email
        FROM ${this.tableName} ah
        LEFT JOIN Users u ON ah.user_id = u.id
        WHERE ah.agent_id = ?
      `;
      const params = [agentId];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ah.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ah.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding action history by agent: ${error.message}`);
    }
  }

  async findByActionType(actionType, options = {}) {
    try {
      let sql = `
        SELECT ah.*, u.username, a.name as agent_name
        FROM ${this.tableName} ah
        LEFT JOIN Users u ON ah.user_id = u.id
        LEFT JOIN Agents a ON ah.agent_id = a.id
        WHERE ah.action_type = ?
      `;
      const params = [actionType];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ah.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ah.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding action history by type: ${error.message}`);
    }
  }

  async findByEntityType(entityType, options = {}) {
    try {
      let sql = `
        SELECT ah.*, u.username, a.name as agent_name
        FROM ${this.tableName} ah
        LEFT JOIN Users u ON ah.user_id = u.id
        LEFT JOIN Agents a ON ah.agent_id = a.id
        WHERE ah.entity_type = ?
      `;
      const params = [entityType];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ah.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ah.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding action history by entity type: ${error.message}`);
    }
  }

  async getActionTypes() {
    try {
      const sql = `SELECT DISTINCT action_type FROM ${this.tableName} WHERE action_type IS NOT NULL ORDER BY action_type`;
      const results = await database.all(sql);
      return results.map(row => row.action_type);
    } catch (error) {
      throw new Error(`Error getting action types: ${error.message}`);
    }
  }

  async getEntityTypes() {
    try {
      const sql = `SELECT DISTINCT entity_type FROM ${this.tableName} WHERE entity_type IS NOT NULL ORDER BY entity_type`;
      const results = await database.all(sql);
      return results.map(row => row.entity_type);
    } catch (error) {
      throw new Error(`Error getting entity types: ${error.message}`);
    }
  }
}

module.exports = new ActionHistory();
