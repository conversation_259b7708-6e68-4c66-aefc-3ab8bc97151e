const BaseModel = require('./BaseModel');
const database = require('../database/connection');

class AgentOutput extends BaseModel {
  constructor() {
    super('Agent_Outputs');
  }

  async findWithDetails(id) {
    try {
      const sql = `
        SELECT ao.*, 
               a.name as agent_name, a.type as agent_type,
               u.username, u.email,
               b.name as brand_name
        FROM ${this.tableName} ao
        LEFT JOIN Agents a ON ao.agent_id = a.id
        LEFT JOIN Users u ON ao.user_id = u.id
        LEFT JOIN Brands b ON a.brand_id = b.id
        WHERE ao.id = ?
      `;
      return await database.get(sql, [id]);
    } catch (error) {
      throw new Error(`Error finding agent output with details: ${error.message}`);
    }
  }

  async findAllWithDetails(options = {}) {
    try {
      let sql = `
        SELECT ao.*, 
               a.name as agent_name, a.type as agent_type,
               u.username, u.email,
               b.name as brand_name
        FROM ${this.tableName} ao
        LEFT JOIN Agents a ON ao.agent_id = a.id
        LEFT JOIN Users u ON ao.user_id = u.id
        LEFT JOIN Brands b ON a.brand_id = b.id
      `;
      const params = [];

      // Add WHERE clause if conditions provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `ao.${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ao.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ao.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      // Add OFFSET clause
      if (options.offset) {
        sql += ` OFFSET ?`;
        params.push(options.offset);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding agent outputs with details: ${error.message}`);
    }
  }

  async findByAgent(agentId, options = {}) {
    try {
      let sql = `
        SELECT ao.*, u.username, u.email
        FROM ${this.tableName} ao
        LEFT JOIN Users u ON ao.user_id = u.id
        WHERE ao.agent_id = ?
      `;
      const params = [agentId];

      // Add additional WHERE conditions
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `ao.${key} = ?`);
        sql += ` AND ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ao.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ao.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding outputs by agent: ${error.message}`);
    }
  }

  async findByUser(userId, options = {}) {
    try {
      let sql = `
        SELECT ao.*, a.name as agent_name, a.type as agent_type
        FROM ${this.tableName} ao
        LEFT JOIN Agents a ON ao.agent_id = a.id
        WHERE ao.user_id = ?
      `;
      const params = [userId];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ao.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY ao.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding outputs by user: ${error.message}`);
    }
  }

  async findBySession(sessionId, options = {}) {
    try {
      let sql = `
        SELECT ao.*, a.name as agent_name, u.username
        FROM ${this.tableName} ao
        LEFT JOIN Agents a ON ao.agent_id = a.id
        LEFT JOIN Users u ON ao.user_id = u.id
        WHERE ao.session_id = ?
      `;
      const params = [sessionId];

      // Add ORDER BY clause
      sql += ` ORDER BY ao.created_at ASC`; // Chronological order for session

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding outputs by session: ${error.message}`);
    }
  }

  async getOutputTypes() {
    try {
      const sql = `SELECT DISTINCT output_type FROM ${this.tableName} WHERE output_type IS NOT NULL ORDER BY output_type`;
      const results = await database.all(sql);
      return results.map(row => row.output_type);
    } catch (error) {
      throw new Error(`Error getting output types: ${error.message}`);
    }
  }

  async getAverageProcessingTime(agentId = null) {
    try {
      let sql = `SELECT AVG(processing_time_ms) as avg_time FROM ${this.tableName}`;
      const params = [];

      if (agentId) {
        sql += ` WHERE agent_id = ?`;
        params.push(agentId);
      }

      const result = await database.get(sql, params);
      return result.avg_time || 0;
    } catch (error) {
      throw new Error(`Error getting average processing time: ${error.message}`);
    }
  }
}

module.exports = new AgentOutput();
