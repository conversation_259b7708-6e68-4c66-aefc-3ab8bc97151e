const BaseModel = require('./BaseModel');
const database = require('../database/connection');

class Role extends BaseModel {
  constructor() {
    super('Roles');
  }

  async findByName(name) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE name = ?`;
      return await database.get(sql, [name]);
    } catch (error) {
      throw new Error(`Error finding role by name: ${error.message}`);
    }
  }

  async findWithUserCount(id) {
    try {
      const sql = `
        SELECT r.*, COUNT(u.id) as user_count
        FROM ${this.tableName} r
        LEFT JOIN Users u ON r.id = u.role_id
        WHERE r.id = ?
        GROUP BY r.id
      `;
      return await database.get(sql, [id]);
    } catch (error) {
      throw new Error(`Error finding role with user count: ${error.message}`);
    }
  }

  async findAllWithUserCounts(options = {}) {
    try {
      let sql = `
        SELECT r.*, COUNT(u.id) as user_count
        FROM ${this.tableName} r
        LEFT JOIN Users u ON r.id = u.role_id
      `;
      const params = [];

      // Add WHERE clause if conditions provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `r.${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      sql += ` GROUP BY r.id`;

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY r.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY r.created_at DESC`;
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding roles with user counts: ${error.message}`);
    }
  }

  async updatePermissions(id, permissions) {
    try {
      const permissionsJson = JSON.stringify(permissions);
      const sql = `UPDATE ${this.tableName} SET permissions = ?, updated_at = ? WHERE id = ?`;
      const result = await database.run(sql, [permissionsJson, new Date().toISOString(), id]);

      if (result.changes === 0) {
        throw new Error(`Role with ID ${id} not found`);
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Error updating role permissions: ${error.message}`);
    }
  }

  async getUsersByRole(roleId) {
    try {
      const sql = `
        SELECT u.id, u.uuid, u.username, u.email, u.first_name, u.last_name, u.is_active, u.created_at
        FROM Users u
        WHERE u.role_id = ?
        ORDER BY u.created_at DESC
      `;
      return await database.all(sql, [roleId]);
    } catch (error) {
      throw new Error(`Error finding users by role: ${error.message}`);
    }
  }
}

module.exports = new Role();
