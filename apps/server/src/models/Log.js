const BaseModel = require('./BaseModel');
const database = require('../database/connection');

class Log extends BaseModel {
  constructor() {
    super('Logs');
  }

  async findWithDetails(id) {
    try {
      const sql = `
        SELECT l.*, 
               u.username, u.email,
               a.name as agent_name
        FROM ${this.tableName} l
        LEFT JOIN Users u ON l.user_id = u.id
        LEFT JOIN Agents a ON l.agent_id = a.id
        WHERE l.id = ?
      `;
      return await database.get(sql, [id]);
    } catch (error) {
      throw new Error(`Error finding log with details: ${error.message}`);
    }
  }

  async findAllWithDetails(options = {}) {
    try {
      let sql = `
        SELECT l.*, 
               u.username, u.email,
               a.name as agent_name
        FROM ${this.tableName} l
        LEFT JOIN Users u ON l.user_id = u.id
        LEFT JOIN Agents a ON l.agent_id = a.id
      `;
      const params = [];

      // Add WHERE clause if conditions provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `l.${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY l.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY l.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      // Add OFFSET clause
      if (options.offset) {
        sql += ` OFFSET ?`;
        params.push(options.offset);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding logs with details: ${error.message}`);
    }
  }

  async findByLevel(level, options = {}) {
    try {
      let sql = `
        SELECT l.*, u.username, a.name as agent_name
        FROM ${this.tableName} l
        LEFT JOIN Users u ON l.user_id = u.id
        LEFT JOIN Agents a ON l.agent_id = a.id
        WHERE l.level = ?
      `;
      const params = [level];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY l.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY l.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding logs by level: ${error.message}`);
    }
  }

  async findBySource(source, options = {}) {
    try {
      let sql = `
        SELECT l.*, u.username, a.name as agent_name
        FROM ${this.tableName} l
        LEFT JOIN Users u ON l.user_id = u.id
        LEFT JOIN Agents a ON l.agent_id = a.id
        WHERE l.source = ?
      `;
      const params = [source];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY l.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY l.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding logs by source: ${error.message}`);
    }
  }

  async findByUser(userId, options = {}) {
    try {
      let sql = `
        SELECT l.*, a.name as agent_name
        FROM ${this.tableName} l
        LEFT JOIN Agents a ON l.agent_id = a.id
        WHERE l.user_id = ?
      `;
      const params = [userId];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY l.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY l.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding logs by user: ${error.message}`);
    }
  }

  async findByAgent(agentId, options = {}) {
    try {
      let sql = `
        SELECT l.*, u.username, u.email
        FROM ${this.tableName} l
        LEFT JOIN Users u ON l.user_id = u.id
        WHERE l.agent_id = ?
      `;
      const params = [agentId];

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY l.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY l.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding logs by agent: ${error.message}`);
    }
  }

  async getLevels() {
    try {
      const sql = `SELECT DISTINCT level FROM ${this.tableName} WHERE level IS NOT NULL ORDER BY level`;
      const results = await database.all(sql);
      return results.map(row => row.level);
    } catch (error) {
      throw new Error(`Error getting log levels: ${error.message}`);
    }
  }

  async getSources() {
    try {
      const sql = `SELECT DISTINCT source FROM ${this.tableName} WHERE source IS NOT NULL ORDER BY source`;
      const results = await database.all(sql);
      return results.map(row => row.source);
    } catch (error) {
      throw new Error(`Error getting log sources: ${error.message}`);
    }
  }

  async deleteOldLogs(daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      const sql = `DELETE FROM ${this.tableName} WHERE created_at < ?`;
      const result = await database.run(sql, [cutoffDate.toISOString()]);
      
      return { deletedCount: result.changes };
    } catch (error) {
      throw new Error(`Error deleting old logs: ${error.message}`);
    }
  }
}

module.exports = new Log();
