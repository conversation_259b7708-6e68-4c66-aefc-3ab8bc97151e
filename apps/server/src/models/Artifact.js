const BaseModel = require('./BaseModel');
const database = require('../database/connection');

class Artifact extends BaseModel {
  constructor() {
    super('Artifacts');
  }

  /**
   * Create a new artifact with JSON content
   * @param {Object} data - Artifact data
   * @param {string} data.json_content - Raw JSON content from AI agent
   * @param {string} [data.session_id] - Session UUID
   * @param {number} [data.user_id] - User ID
   * @returns {Promise<Object>} Created artifact
   */
  async create(data) {
    try {
      // Validate required fields
      if (!data.json_content) {
        throw new Error('json_content is required');
      }

      // Ensure json_content is a string
      const jsonContent = typeof data.json_content === 'string' 
        ? data.json_content 
        : JSON.stringify(data.json_content);

      const artifactData = {
        json_content: jsonContent,
        session_id: data.session_id || null,
        user_id: data.user_id || null
      };

      return await super.create(artifactData);
    } catch (error) {
      throw new Error(`Error creating artifact: ${error.message}`);
    }
  }

  /**
   * Find artifacts by session ID
   * @param {string} sessionId - Session UUID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of artifacts
   */
  async findBySessionId(sessionId, options = {}) {
    try {
      const whereClause = { session_id: sessionId };
      return await this.findAll({ 
        where: whereClause,
        ...options 
      });
    } catch (error) {
      throw new Error(`Error finding artifacts by session ID: ${error.message}`);
    }
  }

  /**
   * Find artifacts by user ID
   * @param {number} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of artifacts
   */
  async findByUserId(userId, options = {}) {
    try {
      const whereClause = { user_id: userId };
      return await this.findAll({ 
        where: whereClause,
        ...options 
      });
    } catch (error) {
      throw new Error(`Error finding artifacts by user ID: ${error.message}`);
    }
  }

  /**
   * Find recent artifacts
   * @param {Object} options - Query options
   * @param {number} [options.limit=50] - Maximum number of artifacts to return
   * @param {number} [options.offset=0] - Number of artifacts to skip
   * @returns {Promise<Array>} Array of recent artifacts
   */
  async findRecent(options = {}) {
    try {
      return await this.findAll({
        orderBy: 'created_at',
        orderDirection: 'DESC',
        limit: options.limit || 50,
        offset: options.offset || 0
      });
    } catch (error) {
      throw new Error(`Error finding recent artifacts: ${error.message}`);
    }
  }

  /**
   * Get artifact with parsed JSON content
   * @param {string} uuid - Artifact UUID
   * @returns {Promise<Object>} Artifact with parsed JSON
   */
  async getWithParsedJson(uuid) {
    try {
      const artifact = await this.findByUuid(uuid);
      if (!artifact) {
        return null;
      }

      // Parse JSON content
      let parsedJson = null;
      try {
        parsedJson = JSON.parse(artifact.json_content);
      } catch (parseError) {
        console.warn(`Failed to parse JSON for artifact ${uuid}:`, parseError.message);
      }

      return {
        ...artifact,
        parsed_json: parsedJson
      };
    } catch (error) {
      throw new Error(`Error getting artifact with parsed JSON: ${error.message}`);
    }
  }

  /**
   * Count artifacts by session
   * @param {string} sessionId - Session UUID
   * @returns {Promise<number>} Count of artifacts
   */
  async countBySession(sessionId) {
    try {
      return await this.count({ session_id: sessionId });
    } catch (error) {
      throw new Error(`Error counting artifacts by session: ${error.message}`);
    }
  }

  /**
   * Count artifacts by user
   * @param {number} userId - User ID
   * @returns {Promise<number>} Count of artifacts
   */
  async countByUser(userId) {
    try {
      return await this.count({ user_id: userId });
    } catch (error) {
      throw new Error(`Error counting artifacts by user: ${error.message}`);
    }
  }

  /**
   * Delete artifacts older than specified days
   * @param {number} days - Number of days
   * @returns {Promise<Object>} Deletion result
   */
  async deleteOlderThan(days) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      
      const sql = `DELETE FROM ${this.tableName} WHERE created_at < ?`;
      const result = await database.run(sql, [cutoffDate.toISOString()]);
      
      return { 
        success: true, 
        deletedCount: result.changes,
        cutoffDate: cutoffDate.toISOString()
      };
    } catch (error) {
      throw new Error(`Error deleting old artifacts: ${error.message}`);
    }
  }
}

module.exports = new Artifact();
