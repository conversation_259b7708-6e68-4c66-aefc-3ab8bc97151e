const db = require('../database/connection');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

// ADK Configuration
const ADK_SERVER_URL = process.env.ADK_SERVER_URL || 'http://127.0.0.1:8000';
const ADK_APP_NAME = process.env.ADK_APP_NAME || 'agency_agent';

class Session {
  /**
   * Create a new session
   */
  static async create(userId, userEmail = null) {
    const sessionId = uuidv4();
    const now = new Date().toISOString();
    let adkSessionId = null;
    const adkUserId = userEmail || '<EMAIL>';

    try {
      // Create ADK session first
      console.log(`Attempting to create ADK session for user: ${adkUserId} at ${ADK_SERVER_URL}`);

      const adkResponse = await axios.post(
        `${ADK_SERVER_URL}/apps/${ADK_APP_NAME}/users/${adkUserId}/sessions`
      );

      if (adkResponse.data && (adkResponse.data.session_id || adkResponse.data.id)) {
        adkSessionId = adkResponse.data.session_id || adkResponse.data.id;
        console.log(`ADK session created successfully: ${adkSessionId}`);
      } else {
        console.warn('ADK session creation returned unexpected response:', adkResponse.data);
      }
    } catch (error) {
      console.error('Failed to create ADK session:', {
        url: `${ADK_SERVER_URL}/apps/${ADK_APP_NAME}/users/${adkUserId}/sessions`,
        error: error.response?.data || error.message,
        status: error.response?.status
      });
      // Continue without ADK session - we'll handle this in the chat endpoint
    }

    const query = `
      INSERT INTO sessions (uuid, adk_session_id, created_by, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?)
    `;

    await db.run(query, [sessionId, adkSessionId, userId, now, now]);

    // Add the creator as a participant with edit access
    await this.addParticipant(sessionId, userId, 'edit');

    return {
      id: sessionId,
      adk_session_id: adkSessionId,
      created_at: now
    };
  }

  /**
   * Find session by ID
   */
  static async findById(sessionId) {
    const query = `
      SELECT uuid, adk_session_id, created_by, created_at, updated_at FROM sessions
      WHERE uuid = ?
    `;

    return await db.get(query, [sessionId]);
  }

  /**
   * Add a participant to a session
   */
  static async addParticipant(sessionId, userId, accessType = 'view') {
    const participantId = uuidv4();
    const now = new Date().toISOString();
    
    const query = `
      INSERT INTO session_participants (
        uuid, session_id, user_id, access_type, is_active, last_active, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await db.run(query, [
      participantId, 
      sessionId, 
      userId, 
      accessType, 
      1, // is_active = true
      now, 
      now, 
      now
    ]);
    
    return participantId;
  }

  /**
   * Get all participants for a session
   */
  static async getParticipants(sessionId) {
    const query = `
      SELECT 
        sp.uuid as participant_id,
        sp.access_type,
        sp.is_active,
        sp.last_active,
        u.id as user_id,
        u.uuid as user_uuid,
        u.username,
        u.email,
        u.first_name,
        u.last_name,
        r.name as role_name
      FROM session_participants sp
      JOIN Users u ON sp.user_id = u.id
      JOIN Roles r ON u.role_id = r.id
      WHERE sp.session_id = ?
    `;
    
    return await db.all(query, [sessionId]);
  }

  /**
   * Update participant's active status
   */
  static async updateParticipantActivity(sessionId, userId, isActive = true) {
    const now = new Date().toISOString();
    
    const query = `
      UPDATE session_participants
      SET is_active = ?, last_active = ?, updated_at = ?
      WHERE session_id = ? AND user_id = ?
    `;
    
    await db.run(query, [isActive ? 1 : 0, now, now, sessionId, userId]);
    
    return true;
  }

  /**
   * Delete a session
   */
  static async delete(sessionId) {
    // First delete all participants
    await db.run(
      'DELETE FROM session_participants WHERE session_id = ?',
      [sessionId]
    );
    
    // Then delete the session
    await db.run(
      'DELETE FROM sessions WHERE uuid = ?',
      [sessionId]
    );
    
    return true;
  }

  /**
   * Get sessions for a user
   */
  static async findByUser(userId) {
    const query = `
      SELECT 
        s.uuid as session_id,
        s.created_at,
        s.updated_at,
        COUNT(sp.id) as participant_count
      FROM sessions s
      JOIN session_participants sp ON s.uuid = sp.session_id
      WHERE s.uuid IN (
        SELECT session_id FROM session_participants WHERE user_id = ?
      )
      GROUP BY s.uuid
      ORDER BY s.updated_at DESC
    `;
    
    return await db.all(query, [userId]);
  }
}

module.exports = Session;