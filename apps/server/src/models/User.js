const BaseModel = require('./BaseModel');
const database = require('../database/connection');

class User extends BaseModel {
  constructor() {
    super('Users');
  }

  async findByEmail(email) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE email = ?`;
      return await database.get(sql, [email]);
    } catch (error) {
      throw new Error(`Error finding user by email: ${error.message}`);
    }
  }

  async findByUsername(username) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE username = ?`;
      return await database.get(sql, [username]);
    } catch (error) {
      throw new Error(`Error finding user by username: ${error.message}`);
    }
  }

  async findWithRole(id) {
    try {
      const sql = `
        SELECT u.*, r.name as role_name, r.description as role_description, r.permissions,
               b.name as brand_name, b.uuid as brand_uuid, b.logo_url as brand_logo
        FROM ${this.tableName} u
        LEFT JOIN Roles r ON u.role_id = r.id
        LEFT JOIN Brands b ON u.brand_id = b.id
        WHERE u.id = ?
      `;
      return await database.get(sql, [id]);
    } catch (error) {
      throw new Error(`Error finding user with role: ${error.message}`);
    }
  }

  async findAllWithRoles(options = {}) {
    try {
      let sql = `
        SELECT u.*, r.name as role_name, r.description as role_description,
               b.name as brand_name, b.uuid as brand_uuid
        FROM ${this.tableName} u
        LEFT JOIN Roles r ON u.role_id = r.id
        LEFT JOIN Brands b ON u.brand_id = b.id
      `;
      const params = [];

      // Add WHERE clause if conditions provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `u.${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY u.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY u.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      console.log('SQL for findAllWithRoles:', sql);
      console.log('Params for findAllWithRoles:', params);

      const users = await database.all(sql, params);
      console.log('Users found:', users.length);
      if (users.length > 0) {
        console.log('Sample user role data:', {
          role_id: users[0].role_id,
          role_name: users[0].role_name
        });
      }
      
      return users;
    } catch (error) {
      console.error('Error finding users with roles:', error);
      throw new Error(`Error finding users with roles: ${error.message}`);
    }
  }

  async updatePassword(id, passwordHash) {
    try {
      const sql = `UPDATE ${this.tableName} SET password_hash = ?, updated_at = ? WHERE id = ?`;
      const result = await database.run(sql, [passwordHash, new Date().toISOString(), id]);

      if (result.changes === 0) {
        throw new Error(`User with ID ${id} not found`);
      }

      return { success: true };
    } catch (error) {
      throw new Error(`Error updating user password: ${error.message}`);
    }
  }

  async deactivate(id) {
    try {
      const sql = `UPDATE ${this.tableName} SET is_active = 0, updated_at = ? WHERE id = ?`;
      const result = await database.run(sql, [new Date().toISOString(), id]);

      if (result.changes === 0) {
        throw new Error(`User with ID ${id} not found`);
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Error deactivating user: ${error.message}`);
    }
  }

  async activate(id) {
    try {
      const sql = `UPDATE ${this.tableName} SET is_active = 1, updated_at = ? WHERE id = ?`;
      const result = await database.run(sql, [new Date().toISOString(), id]);

      if (result.changes === 0) {
        throw new Error(`User with ID ${id} not found`);
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Error activating user: ${error.message}`);
    }
  }

  async findByBrand(brandId, options = {}) {
    try {
      let sql = `
        SELECT u.*, r.name as role_name, r.description as role_description,
               b.name as brand_name, b.uuid as brand_uuid
        FROM ${this.tableName} u
        LEFT JOIN Roles r ON u.role_id = r.id
        LEFT JOIN Brands b ON u.brand_id = b.id
        WHERE u.brand_id = ?
      `;
      const params = [brandId];

      // Add additional WHERE conditions if provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `u.${key} = ?`);
        sql += ` AND ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY u.${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY u.created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding users by brand: ${error.message}`);
    }
  }

  async update(id, userData) {
    try {
      const columns = [];
      const values = [];

      // Build dynamic SET clause
      for (const [key, value] of Object.entries(userData)) {
        if (value !== undefined) {
          columns.push(`${key} = ?`);
          values.push(value);
        }
      }

      // Add updated_at timestamp
      columns.push('updated_at = ?');
      values.push(new Date().toISOString());

      // Add ID for WHERE clause
      values.push(id);

      const sql = `UPDATE ${this.tableName} SET ${columns.join(', ')} WHERE id = ?`;
      console.log('Update SQL:', sql);
      console.log('Update values:', values);
      
      const result = await database.run(sql, values);

      if (result.changes === 0) {
        throw new Error(`User with ID ${id} not found`);
      }

      return await this.findById(id);
    } catch (error) {
      console.error('Error in User.update:', error);
      throw new Error(`Error updating user: ${error.message}`);
    }
  }
}

module.exports = new User();
