const { v4: uuidv4 } = require('uuid');
const database = require('../database/connection');

class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
  }

  async create(data) {
    try {
      // Add UUID and timestamps
      const uuid = uuidv4();
      const now = new Date().toISOString();
      
      const dataWithDefaults = {
        uuid,
        created_at: now,
        updated_at: now,
        ...data
      };

      const columns = Object.keys(dataWithDefaults);
      const placeholders = columns.map(() => '?').join(', ');
      const values = Object.values(dataWithDefaults);

      const sql = `INSERT INTO ${this.tableName} (${columns.join(', ')}) VALUES (${placeholders})`;
      const result = await database.run(sql, values);

      return await this.findById(result.id);
    } catch (error) {
      throw new Error(`Error creating ${this.tableName}: ${error.message}`);
    }
  }

  async findAll(options = {}) {
    try {
      let sql = `SELECT * FROM ${this.tableName}`;
      const params = [];

      // Add WHERE clause if conditions provided
      if (options.where) {
        const conditions = Object.keys(options.where).map(key => `${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(options.where));
      }

      // Add ORDER BY clause
      if (options.orderBy) {
        sql += ` ORDER BY ${options.orderBy}`;
        if (options.orderDirection) {
          sql += ` ${options.orderDirection}`;
        }
      } else {
        sql += ` ORDER BY created_at DESC`;
      }

      // Add LIMIT clause
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(options.limit);
      }

      // Add OFFSET clause
      if (options.offset) {
        sql += ` OFFSET ?`;
        params.push(options.offset);
      }

      return await database.all(sql, params);
    } catch (error) {
      throw new Error(`Error finding ${this.tableName}: ${error.message}`);
    }
  }

  async findById(id) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
      return await database.get(sql, [id]);
    } catch (error) {
      throw new Error(`Error finding ${this.tableName} by ID: ${error.message}`);
    }
  }

  async findByUuid(uuid) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE uuid = ?`;
      return await database.get(sql, [uuid]);
    } catch (error) {
      throw new Error(`Error finding ${this.tableName} by UUID: ${error.message}`);
    }
  }

  async update(id, data) {
    try {
      // Add updated timestamp
      const dataWithTimestamp = {
        ...data,
        updated_at: new Date().toISOString()
      };

      const columns = Object.keys(dataWithTimestamp);
      const setClause = columns.map(col => `${col} = ?`).join(', ');
      const values = [...Object.values(dataWithTimestamp), id];

      const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`;
      const result = await database.run(sql, values);

      if (result.changes === 0) {
        throw new Error(`${this.tableName} with ID ${id} not found`);
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Error updating ${this.tableName}: ${error.message}`);
    }
  }

  async delete(id) {
    try {
      const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
      const result = await database.run(sql, [id]);

      if (result.changes === 0) {
        throw new Error(`${this.tableName} with ID ${id} not found`);
      }

      return { success: true, deletedId: id };
    } catch (error) {
      throw new Error(`Error deleting ${this.tableName}: ${error.message}`);
    }
  }

  async count(where = {}) {
    try {
      let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
      const params = [];

      if (Object.keys(where).length > 0) {
        const conditions = Object.keys(where).map(key => `${key} = ?`);
        sql += ` WHERE ${conditions.join(' AND ')}`;
        params.push(...Object.values(where));
      }

      const result = await database.get(sql, params);
      return result.count;
    } catch (error) {
      throw new Error(`Error counting ${this.tableName}: ${error.message}`);
    }
  }
}

module.exports = BaseModel;
