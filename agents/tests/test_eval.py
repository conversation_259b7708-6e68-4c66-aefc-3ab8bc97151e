import textwrap
import unittest

import dotenv
import pytest

from google.adk.runners import InMemoryRunner
from google.genai.types import Part, UserContent
from agency_agent.agent import root_agent

EXAMPLE_CORE_INPUTS = [
    "Keytruda, Melanoma, United States",
    "Humira, Rheumatoid Arthritis, United States",
    "Eliquis, Prevention of Stroke in Nonvalvular Atrial Fibrillation, United States",
    "Ozempic, Type 2 Diabetes Mellitus, United States",
    "Dupixent, Atopic Dermatitis, United States",
    "Skyrizi, Plaque Psoriasis, United States",
    "Biktarvy, HIV-1 Infection, United States",
    "Jardiance, Type 2 Diabetes Mellitus, United States",
    "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>'s Disease, United States",
    "Opdivo, Non-Small Cell Lung Cancer, United States",
    "Trulicity, Type 2 Diabetes Mellitus, United States",
    "Eylea, Neovascular (Wet) Age-Related Macular Degeneration, United States",
    "Gardasil 9, Prevention of HPV-Related Cervical Cancer, United States",
    "Entyvio, Ulcerative Colitis, United States",
    "Rinvoq, Rheumatoid Arthritis, United States",
    "Xarelto, Treatment of Deep Vein Thrombosis, United States",
    "Trikafta, Cystic Fibrosis (in patients with specific mutations), United States",
    "Mounjaro, Type 2 Diabetes Mellitus, United States",
    "Comirnaty, Prevention of COVID-19, United States",
    "Imbruvica, Chronic Lymphocytic Leukemia, United States"
]

@pytest.fixture(scope="session", autouse=True)
def load_env():
    dotenv.load_dotenv()


class TestAgents(unittest.TestCase):
    """Basic test for the agency agent."""

    def test_happy_path(self):
        """Runs the agent on a simple input and expects a normal response."""
        user_input = textwrap.dedent(
            """
            Double check this:
            Question: who are you
            Answer: Hello! .
        """
        ).strip()

        runner = InMemoryRunner(agent=root_agent)
        session = runner.session_service.create_session(
            app_name=runner.app_name, user_id="test_user"
        )
        content = UserContent(parts=[Part(text=user_input)])
        events = list(
            runner.run(
                user_id=session.user_id,
                session_id=session.id,
                new_message=content,
            )
        )
        response = events[-1].content.parts[0].text

        # The answer in the input is wrong, so we expect the agent to provided a
        # revised answer, and the correct answer should mention research.
        self.assertIn("agent", response.lower())