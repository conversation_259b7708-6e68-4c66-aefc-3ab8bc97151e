"""Defines the Product Profile Agent which generates comprehensive product assessment reports."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool
from ...tools.storage_tools import download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

product_profile_review_agent = Agent(
    model=constants.MODEL,
    name="product_profile_review_agent",
    description="A helpful agent that reviews the output of product_profile_agent.",
    output_key="product_profile_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

product_profile_agent = Agent(
    model=constants.RESEARCH_MODEL,
    name="product_profile_agent",
    description="A helpful agent that generates a report detailing the characteristics of a product.",
    output_key=f"product_profile_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(product_profile_review_agent),
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)