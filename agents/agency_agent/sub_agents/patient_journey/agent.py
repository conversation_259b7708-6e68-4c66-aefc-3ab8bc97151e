"""Defines the Patient Journey Agent which generates comprehensive patient journey reports."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool
from ...tools.storage_tools import download_str_gcs_tool
from ...tools.storage_tools import check_file_exists_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

from ...sub_agents.therapeutic_overview.agent import therapeutic_overview_agent
from ...sub_agents.product_profile.agent import product_profile_agent
from ...sub_agents.customer_profile.agent import customer_profile_agent
from ...sub_agents.market_research.agent import market_research_agent

patient_journey_review_agent = Agent(
    model=constants.MODEL,
    name="patient_journey_review_agent",
    description="A helpful agent that reviews the output of patient_journey_agent.",
    output_key="patient_journey_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

patient_journey_agent = Agent(
    model=constants.RESEARCH_MODEL,
    name="patient_journey_agent",
    description="A helpful agent that creates a high-level narrative outlining the Patient Journey from initial symptoms/awareness to therapeutic intervention. By identifying key Moments That Matter (MTM) and dividing the journey into logical disease-specific phases, it provides a structured foundation for strategic decision-making.",
    output_key=f"patient_journey_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(patient_journey_review_agent),
        AgentTool(agent=therapeutic_overview_agent),
        AgentTool(agent=product_profile_agent),
        AgentTool(agent=customer_profile_agent),
        AgentTool(agent=market_research_agent),
        upload_str_gcs_tool,
        download_str_gcs_tool,
        check_file_exists_gcs_tool,
    ]
)