"""Defines the Archetype Agent which is a test agent for the system."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.datetime_tools import get_current_day_of_week_tool
from ...tools.storage_tools import upload_str_gcs_tool, download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT
from .sub_agents.ai_news_search_agent import ai_news_search_agent

archetype_review_agent = Agent(
    model=constants.MODEL,
    name="archetype_review_agent",
    description="A helpful agent that reviews the output of archetype_agent.",
    output_key="archetype_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

archetype_agent = Agent(
    model=constants.MODEL,
    name="archetype_agent",
    description="A helpful agent that is set up to test tasks in the system. The blueprint for how other agents are built.",
    output_key=f"archetype_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        Agent<PERSON><PERSON>(ai_news_search_agent),
        Agent<PERSON><PERSON>(archetype_review_agent),
        get_current_day_of_week_tool,
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)
