import os

from google.adk.tools.openapi_tool.auth.auth_helpers import token_to_scheme_credential
from google.adk.tools.apihub_tool.apihub_toolset import APIHubToolset

from ...shared_libraries import constants

firefly_scope = "openid,AdobeID,session,additional_info,read_organizations,firefly_api,ff_apis"

auth_scheme, auth_credential = token_to_scheme_credential(
   "apikey", "query", "apikey", os.environ.get("FIREFLY_SERVICES_CLIENT_ID")
)
sample_api_toolset = APIHubToolset(
   name="sample-api-requiring-api-key",
   description="A tool using an API protected by API Key",
   apihub_resource_name="...",
   auth_scheme=auth_scheme,
   auth_credential=auth_credential,
)