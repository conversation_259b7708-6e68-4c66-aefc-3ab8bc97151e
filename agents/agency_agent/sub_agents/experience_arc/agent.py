"""Defines the Experience Arc Agent which generates structured customer experience overviews."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool
from ...tools.storage_tools import download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

from ...sub_agents.patient_journey.agent import patient_journey_agent
from ...sub_agents.market_research.agent import market_research_agent

experience_arc_review_agent = Agent(
    model=constants.MODEL,
    name="experience_arc_review_agent",
    description="A helpful agent that reviews the output of experience_arc_agent.",
    output_key="experience_arc_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

experience_arc_agent = Agent(
    model=constants.RESEARCH_MODEL,
    name="experience_arc_agent",
    description="A helpful agent to provide details around the key stages of a customer experience with a brand.",
    output_key=f"experience_arc_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        Agent<PERSON><PERSON>(experience_arc_review_agent),
        Agent<PERSON><PERSON>(agent=patient_journey_agent),
        Agent<PERSON><PERSON>(agent=market_research_agent),
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)
