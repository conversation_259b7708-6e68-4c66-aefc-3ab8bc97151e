# Full runnable code for the ExperienceConceptAgent example
import logging
from typing import As<PERSON><PERSON>enerator
from typing_extensions import override

from ...shared_libraries import constants

from ...sub_agents.customer_profile.agent import customer_profile_agent
from ...sub_agents.market_research.agent import market_research_agent
from ...sub_agents.experience_arc.agent import experience_arc_agent
from ...sub_agents.persona.agent import persona_agent

from google.adk.agents import LlmAgent, BaseAgent, LoopAgent, SequentialAgent
from google.adk.agents.invocation_context import InvocationContext
from google.adk.tools.agent_tool import AgentTool
from google.genai import types
from google.adk.sessions import InMemorySessionService
from google.adk.runners import Runner
from google.adk.events import Event
from pydantic import BaseModel, Field

# --- Configure Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Custom Orchestrator Agent ---
class ExperienceConceptAgent(BaseAgent):
    """
    Custom agent for content generation and refinement workflow.

    This agent orchestrates a sequence of LLM agents to generate content based on specific inputs,
    critique it, revise it, checks grammar and tone, and potentially
    regenerate the content if the tone is negative.
    """

    # --- Field Declarations for Pydantic ---
    # Declare the agents passed during initialization as class attributes with type hints
    experience_generator: LlmAgent
    critic: LlmAgent
    reviser: LlmAgent
    grammar_check: LlmAgent
    tone_check: LlmAgent
    loop_agent: LoopAgent
    sequential_agent: SequentialAgent

    # model_config allows setting Pydantic configurations if needed, e.g., arbitrary_types_allowed
    model_config = {"arbitrary_types_allowed": True}

    def __init__(
        self,
        name: str,
        experience_generator: LlmAgent,
        critic: LlmAgent,
        reviser: LlmAgent,
        grammar_check: LlmAgent,
        tone_check: LlmAgent,
    ):
        """
        Initializes the ExperienceConceptAgent.

        Args:
            name: The name of the agent.
            experience_generator: An LlmAgent to generate the initial content.
            critic: An LlmAgent to critique the content.
            reviser: An LlmAgent to revise the content based on criticism.
            grammar_check: An LlmAgent to check the grammar.
            tone_check: An LlmAgent to analyze the tone.
        """
        # Create internal agents *before* calling super().__init__
        loop_agent = LoopAgent(
            name="CriticReviserLoop", sub_agents=[critic, reviser], max_iterations=2
        )
        sequential_agent = SequentialAgent(
            name="PostProcessing", sub_agents=[grammar_check, tone_check]
        )

        # Define the sub_agents list for the framework
        sub_agents_list = [
            experience_generator,
            loop_agent,
            sequential_agent,
        ]

        # Pydantic will validate and assign them based on the class annotations.
        super().__init__(
            name=name,
            experience_generator=experience_generator,
            critic=critic,
            reviser=reviser,
            grammar_check=grammar_check,
            tone_check=tone_check,
            loop_agent=loop_agent,
            sequential_agent=sequential_agent,
            sub_agents=sub_agents_list, # Pass the sub_agents list directly
            description="A helpful agent that generates content, iteratively refines it through critique and revision, performs final checks, and crucially, regenerates the content if the final tone check fails.",
        )

    @override
    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """
        Implements the custom orchestration logic for the content workflow.
        Uses the instance attributes assigned by Pydantic (e.g., self.experience_generator).
        """
        logger.info(f"[{self.name}] Starting content generation workflow.")

        # 1. Initial Experience Generation
        logger.info(f"[{self.name}] Running ExperienceGenerator...")
        async for event in self.experience_generator.run_async(ctx):
            logger.info(f"[{self.name}] Event from ExperienceGenerator: {event.model_dump_json(indent=2, exclude_none=True)}")
            yield event

        # Check if content was generated before proceeding
        if "current_experience" not in ctx.session.state or not ctx.session.state["current_experience"]:
             logger.error(f"[{self.name}] Failed to generate initial content. Aborting workflow.")
             return # Stop processing if initial content failed

        logger.info(f"[{self.name}] Experience state after generator: {ctx.session.state.get('current_experience')}")


        # 2. Critic-Reviser Loop
        logger.info(f"[{self.name}] Running CriticReviserLoop...")
        # Use the loop_agent instance attribute assigned during init
        async for event in self.loop_agent.run_async(ctx):
            logger.info(f"[{self.name}] Event from CriticReviserLoop: {event.model_dump_json(indent=2, exclude_none=True)}")
            yield event

        logger.info(f"[{self.name}] Experience state after loop: {ctx.session.state.get('current_experience')}")

        # 3. Sequential Post-Processing (Grammar and Tone Check)
        logger.info(f"[{self.name}] Running PostProcessing...")
        # Use the sequential_agent instance attribute assigned during init
        async for event in self.sequential_agent.run_async(ctx):
            logger.info(f"[{self.name}] Event from PostProcessing: {event.model_dump_json(indent=2, exclude_none=True)}")
            yield event

        # 4. Tone-Based Conditional Logic
        tone_check_result = ctx.session.state.get("tone_check_result")
        logger.info(f"[{self.name}] Tone check result: {tone_check_result}")

        if tone_check_result == "negative":
            logger.info(f"[{self.name}] Tone is negative. Regenerating content...")
            async for event in self.experience_generator.run_async(ctx):
                logger.info(f"[{self.name}] Event from ExperienceGenerator (Regen): {event.model_dump_json(indent=2, exclude_none=True)}")
                yield event
        else:
            logger.info(f"[{self.name}] Tone is not negative. Keeping current content.")
            pass

        logger.info(f"[{self.name}] Workflow finished.")

# --- Define the individual LLM agents ---
experience_generator = LlmAgent(
    name="ExperienceGenerator",
    model=constants.MODEL,
    instruction="""

    You are an expert creative copywriter with extensive knowledge and history working on <Product> that treats <Condition> in <Region>. Your primary function is be given strategic inputs which inform how a template for content should be filled out and written with content that provokes and moves a user 

    Please follow these steps to accomplish the task at hand:
    1. Follow <Gather Required Inputs> section and ensure that the user provides the brand.
    2. Move to the <Steps> section and strictly follow all the steps one by one.
    3. Please adhere to <Key Constraints> when you attempt to answer the user's query.

    <Gather Required Inputs>
        1. If it isn't already known, ask the user for the <Product>, <Condition>, <Region>, and <Audience>.
        2. Get the <Patient Profile> to inform the content that is written. If they don't have it, you can use the tools provided to generate them.
        3. Get the <Persona> for which to write the content to. If they don't have it, ask the user if you can generate one.
        3. Get the <Experience Arc> to write the content againest. This must include details like Moments that Matter, Motivators, Blockers, and a quote which represents the current mindset of the user. If they don't have it, ask the user if you can generate one.
        4. Ask the user which phase in the Experience Arc to focus in on to create an experience.
    </Gather Required Inputs>

    <Steps>
        1. Fill out the provided <Experience Template> to the best of your ability based on the provided inputs, using the first Moment that Matters to serve as the creative prompt, or brief, from which to write content.
        2. If the generated headline or body text includes any sort of a claim (i.e. "This product lowers plaque psoriasis in 12 weeks or less"), identify from the <Product>'s Prescribing Information important safety information that should be included in close proximity to the headline and body, using the footnotes area for acronym definitions or disclaimers and the isi area for the fair balance safety content itself. Make the safety content proportionate to a product's safety characteristics (i.e. very few and minor side effects only need to mention important notices, but products with higher risk should include more safety from the PI).
        3. Continue on to the review.
    </Steps>

    <Key Constraints>
        * Your role is follow the Steps in <Steps> in the specified order.
        * Complete all the steps
        * Wherever anything appears within brackets [] use it as instructions for the content and length with an example after a colon (i.e. [4-10 words: For treating everyday pain])
    </Key Constraints>

    <Experience Template>
        * Product: [Product: Tylenol]
        * Title: [Title: Tylenol: For Life's Aches and Pains]
        * Eyebrow: [Indication the product treats: For Mild to Moderate Headaches and Pain]
        * Headline: [4-12 words] Pain Relief Shouldn't Be Complicated.
        * Body: [Less than 35 word active tone: ]
        * Footnotes: [Optional, only include if a claim is made that needs to be backed up with additional safety information] Use only as directed. Always read and follow product label.
        * cta: [1-4 words: Shop Tylenol products]
        * isi: [Required: Liver warning: This product contains acetaminophen. Severe liver damage may occur if you take more than 4,000 mg of acetaminophen in 24 hours, with other drugs containing acetaminophen, and/or 3 or more alcoholic drinks every day while using this product. Ask a doctor before use if you have liver disease.]
""",
    input_schema=None,
    output_key="current_experience",  # Key for storing output in session state
    tools=[
        AgentTool(agent=customer_profile_agent),
        AgentTool(agent=market_research_agent),
        AgentTool(agent=persona_agent),
        AgentTool(agent=experience_arc_agent),
    ]
)

critic = LlmAgent(
    name="Critic",
    model=constants.MODEL,
    instruction="""You are a story critic. Review the story provided in
session state with key 'current_experience'. Provide 1-2 sentences of constructive criticism
on how to improve it. Focus on plot or character.""",
    input_schema=None,
    output_key="criticism",  # Key for storing criticism in session state
)

reviser = LlmAgent(
    name="Reviser",
    model=constants.MODEL,
    instruction="""You are a story reviser. Revise the story provided in
session state with key 'current_experience', based on the criticism in
session state with key 'criticism'. Output only the revised story.""",
    input_schema=None,
    output_key="current_experience",  # Overwrites the original story
)

grammar_check = LlmAgent(
    name="GrammarCheck",
    model=constants.MODEL,
    instruction="""You are a grammar checker. Check the grammar of the story
provided in session state with key 'current_experience'. Output only the suggested
corrections as a list, or output 'Grammar is good!' if there are no errors.""",
    input_schema=None,
    output_key="grammar_suggestions",
)

tone_check = LlmAgent(
    name="ToneCheck",
    model=constants.MODEL,
    instruction="""You are a tone analyzer. Analyze the tone of the story
provided in session state with key 'current_experience'. Output only one word: 'positive' if
the tone is generally positive, 'negative' if the tone is generally negative, or 'neutral'
otherwise.""",
    input_schema=None,
    output_key="tone_check_result", # This agent's output determines the conditional flow
)

# --- Create the custom agent instance ---
test_experience_concept_agent = ExperienceConceptAgent(
    name="ExperienceConceptAgent",
    experience_generator=experience_generator,
    critic=critic,
    reviser=reviser,
    grammar_check=grammar_check,
    tone_check=tone_check,
)
