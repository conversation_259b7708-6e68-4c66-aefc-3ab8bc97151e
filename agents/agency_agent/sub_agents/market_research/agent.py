"""Defines the Market Research Agent which generates comprehensive patient voice analysis reports."""

# TO-DO:
# - Integrate Meta API to analysis social posts and ads

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool
from ...tools.storage_tools import download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

market_research_review_agent = Agent(
    model=constants.MODEL,
    name="market_research_review_agent",
    description="A helpful agent that reviews the output of market_research_agent.",
    output_key="market_research_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

market_research_agent = Agent(
    model=constants.RESEARCH_MODEL,
    name="market_research_agent",
    description="A helpful agent that performs research into a specific product, returning a detailed overview of patient behavior, engagement trends, and sentiment analysis.",
    output_key=f"market_research_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(market_research_review_agent),
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)