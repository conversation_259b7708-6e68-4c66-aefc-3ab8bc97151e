"""Defines the Product Profile Agent which generates comprehensive product assessment reports."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool
from ...tools.storage_tools import download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

brand_positioning_review_agent = Agent(
    model=constants.MODEL,
    name="brand_positioning_review_agent",
    description="A helpful agent that reviews the output of brand_positioning_agent.",
    output_key="brand_positioning_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

brand_positioning_agent = Agent(
    model=constants.RESEARCH_MODEL,
    name="brand_positioning_agent",
    description="A helpful agent that performs comprehensive brand assessment and SWOT analysis for pharmaceutical products.",
    output_key=f"brand_positioning_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(brand_positioning_review_agent),
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)