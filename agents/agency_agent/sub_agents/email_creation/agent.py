"""Defines the Email Creation Agent which generates professional HTML emails."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool, download_str_gcs_tool
from ...tools.email_tools import html_email_generator_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

email_creation_review_agent = Agent(
    model=constants.MODEL,
    name="email_creation_review_agent",
    description="A helpful agent that reviews the output of email_creation_agent.",
    output_key="email_creation_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

email_creation_agent = Agent(
    model=constants.MODEL,
    name="email_creation_agent",
    description="A helpful agent that creates professional HTML emails based on user inputs and styling preferences. Specializes in generating email-client compatible HTML with custom content and branding.",
    output_key=f"email_creation_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(email_creation_review_agent),
        html_email_generator_tool,
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)
