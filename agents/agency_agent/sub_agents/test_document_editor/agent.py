from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from google.adk.tools import Tool<PERSON>ontext, FunctionTool
from typing import Dict, Any, Optional
from ...shared_libraries import constants
#from ...utils import utils
from agency_agent.tools import storage_tools

GCS_DEFAULT_CONTENT_TYPE = "text/markdown"  # Default content type for uploaded files
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

def upload_content_to_gcs(
    tool_context: ToolContext,
    bucket_name: str,
    content: str,
    file_name: str,
    destination_blob_name: Optional[str] = None,
    content_type: Optional[str] = None
) -> Dict[str, Any]:
    """
    Uploads content to a Google Cloud Storage bucket.
    
    Args:
        tool_context: The tool context for ADK
        bucket_name: The name of the GCS bucket to upload to
        file_name: The name of the artifact file in the ADK session
        destination_blob_name: The name to give the file in GCS (defaults to artifact name)
        content_type: The content type of the file (defaults to PDF)
        
    Returns:
        A dictionary containing the upload status and details
    """
    if content_type is None:
        content_type = GCS_DEFAULT_CONTENT_TYPE
    try:
        # Check if user_content contains a PDF attachment
        if (hasattr(tool_context, "user_content") and 
            tool_context.user_content and 
            tool_context.user_content.parts):
            
            # Look for any file in parts
            file_data = None
            for part in tool_context.user_content.parts:
                if hasattr(part, "inline_data") and part.inline_data:
                    if part.inline_data.mime_type.startswith("application/"):
                        file_data = part.inline_data.data
                        break
            
            if file_data:
                # We found file data in the user message
                if not destination_blob_name:
                    destination_blob_name = file_name
                    if content_type == "application/pdf" and not destination_blob_name.lower().endswith(".pdf"):
                        destination_blob_name += ".pdf"
                
                # Upload to GCS
                client = storage.Client(project=constants.PROJECT)
                bucket = client.bucket(bucket_name)
                blob = bucket.blob(destination_blob_name)
                
                blob.upload_from_string(
                    data=file_data,
                    content_type=content_type
                )
                
                # Generate a URL
                try:
                    url = blob.public_url
                except:
                    url = f"gs://{bucket_name}/{destination_blob_name}"
                
                return {
                    "status": "success",
                    "bucket": bucket_name,
                    "filename": destination_blob_name,
                    "gcs_uri": f"gs://{bucket_name}/{destination_blob_name}",
                    "size_bytes": len(file_data),
                    "content_type": content_type,
                    "url": url,
                    "message": f"Successfully uploaded file to gs://{bucket_name}/{destination_blob_name}"
                }
        
        # If no file found in user content, return error
        return {
            "status": "error",
            "message": "No file found in the current message. Please upload a file and try again.",
            "details": "Files must be attached directly to the current message."
        }
    except GoogleAPIError as e:
        return {
            "status": "error",
            "error_message": str(e),
            "message": f"Failed to upload file: {str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "message": f"An unexpected error occurred: {str(e)}"
        }

test_document_editor_agent = Agent(
    model=constants.MODEL,
    name="document_editor_agent",
    description="A helpful agent that creates, edits, and retrieves a document on GCS.",
    output_key="document_editor",
    instruction="""

        You are a helpful assistant who manages content stored on Google Cloud Storage. 

        You can help users with these main types of tasks:

            1. Upload files to GCS buckets (ask for bucket name and filename)
            2. Create, list, and get details of buckets
            3. List files in buckets
            4. Return the content stored in documents (when in a readable format)
        
        Always confirm operations before executing them, especially for delete operations.

            - For any GCS operation (upload, list, delete, etc.), always include the gs://<bucket-name>/<file> URI in your response to the user. When creating, listing, or deleting items (buckets, files, corpora, etc.), display each as a bulleted list, one per line, using the appropriate emoji 🗂️ for buckets and folders, 📄 for files, etc For example, when listing GCS buckets:
                - 🗂️ gs://bucket-name/
        
        """,
        
    #     Please follow these steps to accomplish the task at hand:
    #     1. Perform what's listed under <Gather Required Inputs> before continuing to the next step.
    #     2. Move to the <Steps> section and strictly follow all the steps one by one.
    #     3. Please adhere to <Key Constraints> when you attempt to answer the user's query.

    #     <Gather Required Inputs>
    #     1. You must have a Google Cloud Storage
    #     </Gather Required Inputs>

    #     <Steps>
    #     1. Make Messages: For each MTM in the Experience Arc, and taking into consideration the Patient Journey, describe a message that could be given to a user in this stage and moment that plays up the motivators and responses to the barriers.
    #       - Follow this format: [Title]: [Message]
    #       - Title = A very brief (<3 word) title for the message (i.e. "Superiority")
    #       - Message = A short (5-15 word) set of text meant to describe what sort of message should be written (i.e. "BRAND was proven superior to COMPETITOR in a head-to-head trial")
    #       - Create three of these titles and messages for each MTM, with each being different like so:
    #         1. SAFE: A message that sounds safe and neutral, based only on strong study and safety data
    #         2. BALANCED: A message that speaks to the overall effectiveness of the product
    #         3. ASSERTIVE: A message that directly compares it to a competitor on the market
    #     </Steps>

    #     <Key Constraints>
    #         - Your role is follow the Steps in <Steps> in the specified order.
    #         - Complete all the steps.
    #         - Do not explain yourself.
    #     </Key Constraints>
    # """,
    tools=[
        # GCS bucket management tools
        storage_tools.create_bucket_tool,
        storage_tools.list_buckets_tool,
        storage_tools.get_bucket_details_tool,
        storage_tools.upload_file_gcs_tool,
        storage_tools.list_blobs_tool,
    ]
)