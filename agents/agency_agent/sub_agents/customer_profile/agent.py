"""Defines the Customer Profile Agent which generates comprehensive customer experience reports for Patients, Healthcare Professionals, and Caregivers."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool
from ...tools.storage_tools import download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

customer_profile_review_agent = Agent(
    model=constants.MODEL,
    name="customer_profile_review_agent",
    description="A helpful agent that reviews the output of customer_profile_agent.",
    output_key="customer_profile_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

customer_profile_agent = Agent(
    model=constants.RESEARCH_MODEL,
    name="customer_profile_agent",
    description="A helpful agent that creates comprehensive reports detailing the multifaceted experience of customers including Patients, Healthcare Professionals, and Caregivers related to specific conditions.",
    output_key=f"customer_profile_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(customer_profile_review_agent),
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)

