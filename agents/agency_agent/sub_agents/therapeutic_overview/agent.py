"""Defines the Therapeutic Overview Agent which generates comprehensive therapeutic area reports."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool
from ...tools.storage_tools import download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

therapeutic_overview_review_agent = Agent(
    model=constants.MODEL,
    name="therapeutic_overview_review_agent",
    description="A helpful agent that reviews the output of therapeutic_overview_agent.",
    output_key="therapeutic_overview_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

therapeutic_overview_agent = Agent(
    model=constants.RESEARCH_MODEL,
    name="therapeutic_overview_agent",
    description="A helpful agent that performs research into a specific therapeutic area, returning a detailed overview.",
    output_key=f"therapeutic_overview_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(therapeutic_overview_review_agent),
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)

