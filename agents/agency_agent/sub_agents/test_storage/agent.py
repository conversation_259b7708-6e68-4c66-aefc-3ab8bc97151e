import os
import re
from google.adk.agents import Agent, AgentResponse, Session
from google.adk.tools import BaseTool
from google.cloud import storage
from google.cloud import aiplatform # For Vertex AI

from ...shared_libraries import constants

# --- Configuration (Set these as environment variables or define directly) ---
# It's highly recommended to use environment variables for these.
# constants.GOOGLE_CLOUD_PROJECT = os.environ.get("constants.GOOGLE_CLOUD_PROJECT")
# constants.GOOGLE_CLOUD_LOCATION = os.environ.get("constants.GOOGLE_CLOUD_LOCATION")
# constants.GOOGLE_CLOUD_STORAGE_BUCKET = os.environ.get("constants.GOOGLE_CLOUD_STORAGE_BUCKET")
# # Vertex AI model identifier (e.g., gemini-1.5-flash-001, gemini-1.0-pro-001)
# constants.MODEL = "gemini-1.5-flash-001"

# --- Helper: Check if configuration is valid ---
def check_configuration():
    if not constants.GOOGLE_CLOUD_PROJECT:
        print("Error: constants.GOOGLE_CLOUD_PROJECT environment variable not set.")
        return False
    if not constants.GOOGLE_CLOUD_LOCATION:
        print("Error: constants.GOOGLE_CLOUD_LOCATION environment variable not set.")
        return False
    if not constants.GOOGLE_CLOUD_STORAGE_BUCKET:
        print("Error: constants.GOOGLE_CLOUD_STORAGE_BUCKET environment variable not set.")
        return False
    return True

# --- GCS Upload Tool ---
class GcsJokeSaverTool(BaseTool):
    name: str = "gcs_joke_saver"
    description: str = "Saves the given joke content to a specified Google Cloud Storage bucket and file name based on the animal."

    def _execute(self, joke_content: str, animal_name: str) -> str:
        """
        Saves the joke to GCS.
        Args:
            joke_content: The text of the joke.
            animal_name: The name of the animal the joke is about.
        Returns:
            A string indicating success or failure, including the GCS URI.
        """
        if not constants.GOOGLE_CLOUD_STORAGE_BUCKET: # Should be caught by global check, but good practice
            return "Error: constants.GOOGLE_CLOUD_STORAGE_BUCKET is not configured for the tool."
        if not joke_content:
            return "Error: No joke content provided to save."
        if not animal_name:
            return "Error: Animal name not provided for filename."

        try:
            # Initialize GCS client
            # The project can be inferred if GOOGLE_APPLICATION_CREDENTIALS is set
            # or running in a GCP environment. Explicitly passing project for clarity.
            storage_client = storage.Client(project=constants.GOOGLE_CLOUD_PROJECT)
            bucket = storage_client.bucket(constants.GOOGLE_CLOUD_STORAGE_BUCKET)

            # Sanitize animal_name for a safe filename
            safe_animal_name = re.sub(r'[^a-zA-Z0-9_]', '_', animal_name.lower())
            # Create a somewhat unique filename
            destination_blob_name = f"jokes/{safe_animal_name}_joke_{os.urandom(4).hex()}.txt"

            blob = bucket.blob(destination_blob_name)
            blob.upload_from_string(joke_content, content_type="text/plain")

            gcs_uri = f"gs://{constants.GOOGLE_CLOUD_STORAGE_BUCKET}/{destination_blob_name}"
            print(f"Tool: Joke about '{animal_name}' successfully saved to {gcs_uri}")
            return f"Joke about '{animal_name}' saved to {gcs_uri}"
        except Exception as e:
            print(f"Tool Error: Failed to save joke to GCS. {e}")
            return f"Error saving joke to GCS: {str(e)}"

# --- Joke Master Agent ---
class JokeMasterAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Initialize Vertex AI client context if constants.GOOGLE_CLOUD_PROJECT and constants.GOOGLE_CLOUD_LOCATION are set
        if constants.GOOGLE_CLOUD_PROJECT and constants.GOOGLE_CLOUD_LOCATION:
            try:
                aiplatform.init(project=constants.GOOGLE_CLOUD_PROJECT, location=constants.GOOGLE_CLOUD_LOCATION)
                print(f"Vertex AI initialized for project '{constants.GOOGLE_CLOUD_PROJECT}' in '{constants.GOOGLE_CLOUD_LOCATION}'.")
            except Exception as e:
                print(f"Warning: Failed to initialize Vertex AI SDK: {e}")
        else:
            print("Warning: constants.GOOGLE_CLOUD_PROJECT or constants.GOOGLE_CLOUD_LOCATION not set. Vertex AI features might be limited or fail.")

    async def process(self, session: Session, text: str | None = None, **kwargs) -> AgentResponse | None:
        """
        Orchestrates asking for an animal, generating a joke, and saving it.
        """
        print(f"\nJokeMasterAgent (Session ID: {session.session_id}) processing...")
        if not check_configuration():
            return AgentResponse("There's a configuration error. Please check the environment variables (constants.GOOGLE_CLOUD_PROJECT, constants.GOOGLE_CLOUD_LOCATION, constants.GOOGLE_CLOUD_STORAGE_BUCKET).")

        animal = session.get_state().get("animal_for_joke")

        if not animal:
            # Try to get animal from initial user input `text`
            if text and text.strip() and not text.lower().startswith(("hi", "hello", "joke")): # Basic heuristic
                potential_animal = text.strip()
                # You might add a simple LLM call here to confirm if `potential_animal` is indeed an animal
                # For simplicity, we'll assume it is if provided this way.
                animal = potential_animal
                session.get_state()["animal_for_joke"] = animal
                print(f"Animal '{animal}' received from initial input text.")
            else:
                # If no animal in session or initial text, ask the user
                try:
                    # `ask_user_text` is suitable for interactive ADK runtimes (like CLI or web UI)
                    user_animal_input = await self.runtime.ask_user_text("What animal should the joke be about?")
                    if not user_animal_input or not user_animal_input.strip():
                        return AgentResponse("No animal provided. If you want a joke, tell me an animal!")
                    animal = user_animal_input.strip()
                    session.get_state()["animal_for_joke"] = animal
                    print(f"Animal '{animal}' received via ask_user_text.")
                except Exception as e:
                    # This can happen if ask_user_text is not supported (e.g., non-interactive test run)
                    print(f"Could not use ask_user_text: {e}. Please provide animal name in initial prompt (e.g., 'tell me a joke about a cat').")
                    return AgentResponse("I need an animal to make a joke! Try 'Tell me a joke about a cat'.")

        if not animal: # Should ideally not be reached if logic above is sound
            return AgentResponse("I'm sorry, I didn't catch which animal you'd like a joke about.")

        # 2. Generate the joke using the agent's configured LLM (Vertex AI Gemini model)
        joke_prompt = f"Tell me a short, clean, and funny joke about a {animal}. Make it suitable for all audiences. The joke should be original."
        print(f"Generating joke for '{animal}' with prompt: '{joke_prompt[:100]}...'")

        try:
            # Using the agent's own LLM as configured via platform_config
            llm_response = await self.llm.predict_async(prompt=joke_prompt)
            joke_text = llm_response.text # Accessing text might vary slightly based on LLMResponse structure

            if not joke_text or not joke_text.strip():
                print(f"LLM returned empty joke for {animal}.")
                return AgentResponse(f"Hmm, I'm stumped! I couldn't think of a joke about a {animal} right now.")
            print(f"Generated joke: \"{joke_text}\"")
        except Exception as e:
            print(f"Error generating joke with Vertex AI LLM: {e}")
            return AgentResponse(f"Sorry, there was an error trying to make a joke about a {animal}. The LLM might be unavailable or misconfigured.")

        # 3. Store the joke in GCS using the tool
        print(f"Attempting to save joke about '{animal}' to GCS bucket '{constants.GOOGLE_CLOUD_STORAGE_BUCKET}'.")
        gcs_tool_instance = self.tools.get("gcs_joke_saver") # Get the tool instance

        if not gcs_tool_instance:
            print("Error: GCS saving tool 'gcs_joke_saver' is not available or not registered with the agent.")
            return AgentResponse("Internal error: The joke saving tool is missing.")

        # ADK tools' _execute methods are typically synchronous.
        # If the tool's action is I/O bound and you made _execute async, you'd await it.
        # Here, we call the synchronous _execute method.
        gcs_save_result = gcs_tool_instance._execute(joke_content=joke_text, animal_name=animal)
        print(f"GCS save tool execution result: {gcs_save_result}")

        # Clear the animal from session state for the next joke, so it asks again
        session.get_state().pop("animal_for_joke", None)

        final_response_message = f"Okay, here's a joke about a {animal}:\n\n{joke_text}\n\n{gcs_save_result}"
        return AgentResponse(final_response_message)

# --- ADK Entry Point (for `adk run .` or `adk serve`) ---
# This defines the agent that ADK will run.
# The agent_id "root" is a common convention for the main agent.
root_agent = JokeMasterAgent(
    name="JokeMasterVertexAgent", # A descriptive name for your agent
    description="An agent that tells jokes about animals specified by the user, using Vertex AI for joke generation, and saves them to Google Cloud Storage.",
    instruction=( # General instruction for the agent if it were more LLM-driven for its main flow
        "You are a friendly and witty Joke Bot. Your primary function is to engage users by asking them "
        "what animal they'd like a joke about. Once an animal is specified, you will generate a short, "
        "clean, and funny joke related to that animal. After crafting the joke, you must save it to "
        "Google Cloud Storage. Finally, present the joke to the user and confirm that it has been saved, "
        "providing the storage location."
    ),
    model=constants.MODEL, # This is the Vertex AI model identifier
    platform_config={ # Crucial for Vertex AI integration with ADK
        "type": "vertex_ai", # Specifies that the agent's LLM runs on Vertex AI
        "project_id": constants.GOOGLE_CLOUD_PROJECT,
        "location": constants.GOOGLE_CLOUD_LOCATION,
        # Optional: add model_parameters like temperature, max_output_tokens if needed
        # "model_parameters": {
        #     "temperature": 0.7,
        #     "max_output_tokens": 150
        # }
    },
    tools=[GcsJokeSaverTool()], # Register the GCS tool with the agent
    # enable_dynamic_tool_calls=False, # Set to True if LLM should decide when to call tools based on description
                                     # For this explicit flow, False is fine.
)

# This allows ADK to find your agent when you run `adk run .` or `adk serve`
# if your file is `agent.py` and `root_agent` is defined.
# Alternatively, you can create a `main.py` with an `agent_map`.