from datetime import datetime
from google.adk.agents.llm_agent import Agent
from google.adk.tools import google_search
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from google.adk.sessions import DatabaseSessionService
# from ...utils import utils

# --- Boilerplate Setup ---
db_url = "sqlite:///./agency_data.db"
session_service = DatabaseSessionService(db_url=db_url)

# --- Atomic Agents for Data Gathering ---

# Note: In a real-world scenario, these would use specialized tools 
# (e.g., PubMed API, ClinicalTrials.gov API), but we use google_search for this example.

clinical_trial_agent = Agent(
    model=constants.MODEL,
    name="clinical_trial_agent",
    description="A specialized agent that searches for clinical trial results on a given product or medical condition.",
    output_key="trial_results",
    instruction="Perform a targeted search for clinical trial results for <Condition> with <Product> used as part of the study within the <Region>. Focus on official registries, publications, and medical journals like PubMed and ClinicalTrials.gov.",
    tools=[google_search]
)

claims_data_agent = Agent(
    model=constants.MODEL,
    name="claims_data_agent",
    description="A specialized agent that searches for medical claims data or related market research.",
    output_key="claims_data",
    instruction="Perform a targeted search for medical claims data or analysis related to the specified <Product>, <Condition>, and <Region>. Look for datasets, reports, and industry analysis.",
    tools=[google_search]
)

secondary_research_agent = Agent(
    model=constants.MODEL,
    name="secondary_research_agent",
    description="A specialized agent that performs secondary market research for the life sciences sector.",
    output_key="market_research",
    instruction="Perform a broad secondary market research search on the provided <Condition> and <Region>. Consolidate findings on market size, competitors, and strategic outlook.",
    tools=[google_search]
)

clinical_agent = Agent(
    model=constants.MODEL,
    name="clinical_agent",
    description="Consolidates clinical trial data and market research into structured outputs to inform strategic objectives for marketing and product assessments.",
    output_key="clinical_agent_output",
    instruction="""
        Your role is to function as a **Clinical and Business Foundation AI Agent**. 
        Your primary function is to consolidate diverse data sources into structured, strategic outputs for marketing and product assessment. Please adhere to the process below.

        Please follow these steps to accomplish the task at hand, only continuing when the user is satisfied with the results of each step:
        1. Perform what's listed under <Gather Required Inputs>.
        2. Move to the <Steps> section and strictly follow all the steps one by one.
        3. Run the archetype_review_agent tool to ensure the content created in Steps passes checks before proceeding.
        4. Formalize the output by performing the steps listed under <Prepare Output>.

        <Gather Required Inputs>
        1. If it isn't already known, ask the user for the <Product>, <Condition>, <Region>, and <Audience>.
        2. Use the available tools to conduct research:
        - **Clinical Trial Results:** Use the `clinical_trial_agent`.
        - **Claims Data:** Use the `claims_data_agent`.
        - **Secondary Research:** Use the `secondary_research_agent`.
        3. Request the following from the user (as they are not searchable):**
        - **Scientific Insights:** Key scientific questions or insights to investigate.
        - **Target Product Profile:** The desired characteristics of the product.
        - **Client Materials:** Any additional provided documents or data.
        </Gather Required Inputs>
        
        <Steps>
        Once all inputs are gathered, proceed with your analysis.
        
        1. **Synthesize:** Integrate the findings from the research tools (`trial_results`, `claims_data`, `market_research`) with the user-provided materials (`Scientific Insights`, `Target Product Profile`, `Client Materials`).
        2. **Analyze:** Evaluate the consolidated data to identify key insights, feasibility considerations, medical evidence, and market positioning.
        </Steps>
        
        <Prepare Outputs>
        Finally, formalize your analysis into the three distinct deliverables listed below. Structure your final response as a single JSON object where each key corresponds to a deliverable.

        1.  **`dossier_feasibility_report`**: Write a summary report that outlines the product's feasibility, the competitive landscape, and key scientific insights derived from the data.
        2.  **`medical_data_download`**: Create a structured compilation of the most relevant raw medical data points, clinical trial outcomes, and claims data statistics.
        3.  **`product_attribute_map`**: Generate a map or list of key product attributes. This map should be derived from the TPP and validated against the clinical evidence and market research you gathered.
        </Prepare Outputs>
    """,
    tools=[
        AgentTool(clinical_trial_agent),
        AgentTool(claims_data_agent),
        AgentTool(secondary_research_agent),
    ]
)