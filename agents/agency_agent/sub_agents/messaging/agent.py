"""Defines the Messaging Agent which creates actionable content recommendations."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.storage_tools import upload_str_gcs_tool
from ...tools.storage_tools import download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

from ...sub_agents.patient_journey.agent import patient_journey_agent
from ...sub_agents.experience_arc.agent import experience_arc_agent

messaging_review_agent = Agent(
    model=constants.MODEL,
    name="messaging_review_agent",
    description="A helpful agent that reviews the output of messaging_agent.",
    output_key="messaging_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

messaging_agent = Agent(
    model=constants.RESEARCH_MODEL,
    name="messaging_agent",
    description="A helpful agent that creates actionable content recommendations to be used ahead of content creation.",
    output_key=f"messaging_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(messaging_review_agent),
        <PERSON><PERSON><PERSON>(agent=patient_journey_agent),
        Agent<PERSON><PERSON>(agent=experience_arc_agent),
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)