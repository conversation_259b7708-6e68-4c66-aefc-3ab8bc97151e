"""Defines the Agency Agent which oversees and orchestrates Agent and A2A communication"""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool

from .shared_libraries import constants

# from .sub_agents.archetype.agent import archetype_agent
from .sub_agents.therapeutic_overview.agent import therapeutic_overview_agent
from .sub_agents.product_profile.agent import product_profile_agent
from .sub_agents.customer_profile.agent import customer_profile_agent
from .sub_agents.brand_positioning.agent import brand_positioning_agent
from .sub_agents.patient_journey.agent import patient_journey_agent
from .sub_agents.market_research.agent import market_research_agent
from .sub_agents.messaging.agent import messaging_agent

# from .sub_agents.persona.agent import persona_agent

from .sub_agents.experience_arc.agent import experience_arc_agent

# from .sub_agents.clinical.agent import clinical_agent
# from .sub_agents.test_story_creator.agent import test_story_creator_agent
from .sub_agents.test_experience_concept.agent import test_experience_concept_agent
from .sub_agents.email_creation.agent import email_creation_agent
# from .sub_agents.test_document_editor.agent import test_document_editor_agent
# from .sub_agents.test_storage.agent import test_storage_agent

# from .tools.memory_tools import store_brand_context_tool, get_brand_context_tool

from .sub_agents.email_creation.agent import email_creation_agent

from . import prompt

root_agent = Agent(
    model=constants.MODEL,
    name=constants.AGENT_NAME,
    description=constants.DESCRIPTION,
    instruction=prompt.ROOT_PROMPT,
    sub_agents=[
        
        # archetype_agent,
        
        # Business Strategy & Brand Differentiation
        therapeutic_overview_agent,
        product_profile_agent,
        customer_profile_agent,
        brand_positioning_agent,
        patient_journey_agent,
        market_research_agent,
        
        # Omnichannel & Transformation
        experience_arc_agent,
        messaging_agent,
        
        # Content Creation Delivery and Deployment
        test_experience_concept_agent,
        email_creation_agent,

        # clinical_agent,        
        # therapeutic_overview_agent,
        # product_profile_agent,
        # persona_agent,
        # customer_profile_agent,
        # develop_messaging_agent,
        
        # test_story_creator_agent,
        
    ],
    tools=[
        # AgentTool(store_brand_context_tool),
        # AgentTool(get_brand_context_tool),
    ]
)