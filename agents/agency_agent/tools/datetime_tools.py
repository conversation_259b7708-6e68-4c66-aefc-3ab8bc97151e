"""
Datetime Tools for ADK

This module provides tools for working with dates and times
to be used with the Agent Development Kit (ADK).
"""

from datetime import datetime
from google.adk.tools import ToolContext, FunctionTool

def get_current_day_of_week(tool_context: ToolContext) -> dict:
    """
    Returns the current day of the week as a full string name (e.g., 'Sunday').
    """
    return {
        "current_day": datetime.now().strftime('%A')
    }

# Create FunctionTool from the function
get_current_day_of_week_tool = FunctionTool(get_current_day_of_week)
