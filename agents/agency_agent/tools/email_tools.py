"""Email generation tools for creating HTML emails."""

from google.adk.tools import FunctionTool
from google.adk.tools.tool_context import Tool<PERSON>ontext
from typing import Dict, Any, Optional


def generate_html_email(
    tool_context: ToolContext,
    subject_line: str,
    headline: str,
    body_copy: str,
    cta_text: str,
    cta_url: str = "#",
    primary_color: str = "#007bff",
    accent_color: str = "#f8f9fa",
    body_text_color: str = "#333333",
    font_family: str = "Arial, sans-serif",
    email_width: str = "600px",
    brand_name: str = "",
    logo_url: str = "",
    website_url: str = "",
    footer_text: str = "",
    additional_sections: str = ""
) -> Dict[str, Any]:
    """
    Generates a complete HTML email with the provided content and styling.
    
    Args:
        tool_context: The tool context for ADK
        subject_line: Email subject line
        headline: Main headline/title for the email
        body_copy: Main body content/message
        cta_text: Call-to-action button text
        cta_url: URL for the call-to-action button
        primary_color: Primary brand color (hex code)
        accent_color: Accent/background color (hex code)
        body_text_color: Body text color (hex code)
        font_family: Font family for the email
        email_width: Width of the email container
        brand_name: Company/brand name
        logo_url: URL to brand logo image
        website_url: Company website URL
        footer_text: Additional footer information
        additional_sections: Any additional HTML content sections
        
    Returns:
        A dictionary containing the generated HTML email
    """
    try:
        # Generate the HTML email template
        html_email = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{subject_line}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style type="text/css">
        /* Reset styles */
        body, table, td, p, a, li, blockquote {{
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }}
        table, td {{
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }}
        img {{
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }}
        
        /* Email styles */
        .email-container {{
            max-width: {email_width};
            margin: 0 auto;
            font-family: {font_family};
        }}
        
        @media only screen and (max-width: 600px) {{
            .email-container {{
                width: 100% !important;
                max-width: 100% !important;
            }}
            .mobile-padding {{
                padding: 20px !important;
            }}
        }}
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: {accent_color};">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td style="padding: 20px 0;">
                <div class="email-container">
                    <!-- Email Header -->
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">"""

        # Add logo section if logo URL is provided
        if logo_url:
            html_email += f"""
                        <tr>
                            <td style="padding: 30px 40px 20px; text-align: center;">
                                <img src="{logo_url}" alt="{brand_name} Logo" style="max-width: 200px; height: auto;">
                            </td>
                        </tr>"""

        # Add main content
        html_email += f"""
                        <!-- Main Content -->
                        <tr>
                            <td class="mobile-padding" style="padding: 20px 40px;">
                                <!-- Headline -->
                                <h1 style="margin: 0 0 20px; font-size: 28px; line-height: 1.2; color: {primary_color}; font-weight: bold; text-align: center;">
                                    {headline}
                                </h1>
                                
                                <!-- Body Copy -->
                                <div style="margin: 0 0 30px; font-size: 16px; line-height: 1.6; color: {body_text_color};">
                                    {body_copy.replace(chr(10), '<br>')}
                                </div>"""

        # Add additional sections if provided
        if additional_sections:
            html_email += f"""
                                <!-- Additional Sections -->
                                <div style="margin: 0 0 30px;">
                                    {additional_sections}
                                </div>"""

        # Add CTA button
        html_email += f"""
                                <!-- Call to Action -->
                                <div style="text-align: center; margin: 30px 0;">
                                    <a href="{cta_url}" style="display: inline-block; padding: 15px 30px; background-color: {primary_color}; color: #ffffff; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px;">
                                        {cta_text}
                                    </a>
                                </div>
                            </td>
                        </tr>"""

        # Add footer
        footer_content = footer_text if footer_text else f"© 2024 {brand_name}. All rights reserved."
        if website_url:
            footer_content += f' | <a href="{website_url}" style="color: {primary_color};">Visit our website</a>'

        html_email += f"""
                        <!-- Footer -->
                        <tr>
                            <td style="padding: 20px 40px 30px; text-align: center; border-top: 1px solid #e0e0e0;">
                                <p style="margin: 0; font-size: 14px; color: #666666; line-height: 1.4;">
                                    {footer_content}
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>"""

        return {
            "status": "success",
            "html_email": html_email,
            "subject_line": subject_line,
            "email_width": email_width,
            "primary_color": primary_color,
            "accent_color": accent_color,
            "message": "HTML email generated successfully"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "message": f"Failed to generate HTML email: {str(e)}"
        }


# Create FunctionTool from the function
html_email_generator_tool = FunctionTool(generate_html_email)
