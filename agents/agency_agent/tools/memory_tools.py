"""
Memory Tools for ADK

This module provides tools for storing and retrieving memory
to be used with the Agent Development Kit (ADK).
"""

from google.adk.tools import ToolContext, FunctionTool
from typing import Dict, Any

def store_brand_context(
    context: ToolContext,
    product: str,  # The pharmaceutical brand name, e.g., RINVOQ
    condition: str,  # The disease state or indication, e.g., Alopecia
    region: str,  # The geographical region, e.g., United States
) -> Dict[str, Any]:
    """
    Takes the user's initial input for brand, condition, and region
    and stores it in the session state.
    """
    # Store the captured values in the session state
    context.state["Product"] = product
    context.state["Condition"] = condition
    context.state["Region"] = region

    # Formulate a confirmation message for the user
    confirmation_message = (
        f"Thank you. I have set the focus to Brand: **{product}**, "
        f"Disease State: **{condition}**, and Region: **{region}**. "
        f"How can I help you today?"
    )

    # Return the confirmation to the user
    return {
        "message": confirmation_message
    }
    
def get_brand_context(
    context: ToolContext
) -> Dict[str, any]:
    """
    Retrieves the brand, condition, and region from the session state.
    """
    return {
        "product": context.state.get("Product"),
        "condition": context.state.get("Condition"),
        "region": context.state.get("Region")
    }


store_brand_context_tool = FunctionTool(store_brand_context)
# get_brand_context_tool = FunctionTool(get_brand_context)
