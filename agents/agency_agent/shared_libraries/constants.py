"""Defines constants."""

import os
import dotenv

dotenv.load_dotenv()

AGENT_NAME = "agency_agent"
DESCRIPTION = "A helpful assistant for performing the Strategic work ahead of content creation."
PROJECT = os.getenv("GOOGLE_CLOUD_PROJECT", "")
LOCATION = os.getenv("GOOGLE_CLOUD_LOCATION", "")
GOOGLE_CLOUD_STORAGE = os.getenv("GOOGLE_CLOUD_STORAGE", "")
GCS_DEFAULT_STORAGE_CLASS = os.environ.get("GCS_DEFAULT_STORAGE_CLASS", "")
GCS_DEFAULT_LOCATION = os.environ.get("GOOGLE_CLOUD_STORAGE_BUCKET", "")
MODEL = os.getenv("MODEL", "")
RESEARCH_MODEL = os.getenv("RESEARCH_MODEL", "")
DISABLE_WEB_DRIVER = int(os.getenv("DISABLE_WEB_DRIVER", 0))