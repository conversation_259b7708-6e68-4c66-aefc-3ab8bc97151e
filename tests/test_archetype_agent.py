import pytest
from unittest.mock import patch, MagicMock
from agents.agency_agent.sub_agents.archetype.agent import archetype_agent
from agents.agency_agent.shared_libraries import constants

class TestArchetypeAgent:
    
    def test_agent_initialization(self):
        """Test that the agent initializes with correct parameters."""
        assert archetype_agent.name == "archetype_agent"
        assert archetype_agent.model == constants.MODEL
        assert "archetype_agent_output" in archetype_agent.output_key
    
    @patch("agents.agency_agent.tools.datetime_tools.get_current_day_of_week_tool._execute")
    @patch("agents.agency_agent.sub_agents.archetype.sub_agents.ai_news_search_agent.ai_news_search_agent._execute")
    @patch("agents.agency_agent.tools.storage_tools.download_str_gcs_tool._execute")
    @patch("agents.agency_agent.tools.storage_tools.upload_str_gcs_tool._execute")
    def test_agent_workflow(self, mock_upload, mock_download, mock_search, mock_day):
        """Test the complete workflow of the agent."""
        # Setup mocks
        mock_download.return_value = '["AI", "Healthcare", "Finance"]'
        mock_search.return_value = "Latest news about AI: New breakthrough in LLMs"
        mock_day.return_value = "Monday"
        mock_upload.return_value = "gs://beeswax-storage/Product/archetype_output.json"
        
        # Test execution would go here
        # This would involve calling the agent with a test session
        pass