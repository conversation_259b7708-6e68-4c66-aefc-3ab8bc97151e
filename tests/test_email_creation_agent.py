"""Tests for the Email Creation Agent."""

import pytest
from unittest.mock import patch, MagicMock
from agents.agency_agent.sub_agents.email_creation.agent import email_creation_agent
from agents.agency_agent.tools.email_tools import generate_html_email


class TestEmailCreationAgent:
    """Test cases for the Email Creation Agent."""
    
    def test_agent_initialization(self):
        """Test that the agent is properly initialized."""
        assert email_creation_agent.name == "email_creation_agent"
        assert email_creation_agent.description is not None
        assert len(email_creation_agent.tools) > 0
    
    def test_html_email_generator_tool_basic(self):
        """Test the HTML email generator tool with basic inputs."""
        # Mock tool context
        mock_context = MagicMock()
        
        # Test basic email generation
        result = generate_html_email(
            tool_context=mock_context,
            subject_line="Test Email",
            headline="Welcome to Our Service",
            body_copy="Thank you for joining us. We're excited to have you on board!",
            cta_text="Get Started",
            cta_url="https://example.com/start",
            brand_name="Test Company"
        )
        
        assert result["status"] == "success"
        assert "html_email" in result
        assert "Test Email" in result["html_email"]
        assert "Welcome to Our Service" in result["html_email"]
        assert "Get Started" in result["html_email"]
        assert "Test Company" in result["html_email"]
    
    def test_html_email_generator_with_custom_styling(self):
        """Test the HTML email generator with custom styling."""
        mock_context = MagicMock()
        
        result = generate_html_email(
            tool_context=mock_context,
            subject_line="Custom Styled Email",
            headline="Custom Headline",
            body_copy="This is a custom styled email.",
            cta_text="Click Here",
            primary_color="#ff6b35",
            accent_color="#f7f3e9",
            body_text_color="#2c3e50",
            font_family="Georgia, serif",
            email_width="700px",
            brand_name="Custom Brand"
        )
        
        assert result["status"] == "success"
        assert "#ff6b35" in result["html_email"]  # Primary color
        assert "#f7f3e9" in result["html_email"]  # Accent color
        assert "#2c3e50" in result["html_email"]  # Body text color
        assert "Georgia, serif" in result["html_email"]  # Font family
        assert "700px" in result["html_email"]  # Email width
    
    def test_html_email_generator_with_logo_and_footer(self):
        """Test the HTML email generator with logo and custom footer."""
        mock_context = MagicMock()
        
        result = generate_html_email(
            tool_context=mock_context,
            subject_line="Email with Logo",
            headline="Brand Email",
            body_copy="This email includes our logo and custom footer.",
            cta_text="Learn More",
            brand_name="Logo Brand",
            logo_url="https://example.com/logo.png",
            website_url="https://example.com",
            footer_text="Custom footer information here."
        )
        
        assert result["status"] == "success"
        assert "https://example.com/logo.png" in result["html_email"]
        assert "Logo Brand" in result["html_email"]
        assert "https://example.com" in result["html_email"]
        assert "Custom footer information here." in result["html_email"]
    
    def test_html_email_generator_with_additional_sections(self):
        """Test the HTML email generator with additional content sections."""
        mock_context = MagicMock()
        
        additional_content = """
        <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <h3>Special Offer</h3>
            <p>Get 20% off your first purchase!</p>
        </div>
        """
        
        result = generate_html_email(
            tool_context=mock_context,
            subject_line="Email with Extra Sections",
            headline="Special Promotion",
            body_copy="Check out our latest offers below.",
            cta_text="Shop Now",
            additional_sections=additional_content,
            brand_name="Promo Brand"
        )
        
        assert result["status"] == "success"
        assert "Special Offer" in result["html_email"]
        assert "Get 20% off" in result["html_email"]
    
    def test_html_email_generator_error_handling(self):
        """Test error handling in the HTML email generator."""
        mock_context = MagicMock()
        
        # Test with minimal required parameters
        result = generate_html_email(
            tool_context=mock_context,
            subject_line="",  # Empty subject
            headline="Test",
            body_copy="Test body",
            cta_text="Test CTA"
        )
        
        # Should still succeed with empty subject
        assert result["status"] == "success"
    
    @patch("agents.agency_agent.tools.storage_tools.upload_str_gcs_tool._execute")
    @patch("agents.agency_agent.tools.email_tools.html_email_generator_tool._execute")
    def test_agent_workflow_mock(self, mock_email_generator, mock_upload):
        """Test the complete workflow of the agent with mocked tools."""
        # Setup mocks
        mock_email_generator.return_value = {
            "status": "success",
            "html_email": "<html><body>Test Email</body></html>",
            "message": "Email generated successfully"
        }
        mock_upload.return_value = "gs://beeswax-storage/TestBrand/emails/email_creation_output.html"
        
        # This would be a more complete integration test
        # For now, just verify the mocks can be set up
        assert mock_email_generator is not None
        assert mock_upload is not None


class TestEmailToolIntegration:
    """Integration tests for email tools."""
    
    def test_email_html_structure(self):
        """Test that generated HTML has proper email structure."""
        mock_context = MagicMock()
        
        result = generate_html_email(
            tool_context=mock_context,
            subject_line="Structure Test",
            headline="Test Headline",
            body_copy="Test body content",
            cta_text="Test CTA"
        )
        
        html = result["html_email"]
        
        # Check for essential email HTML structure
        assert "<!DOCTYPE html>" in html
        assert "<html lang=\"en\">" in html
        assert "<meta charset=\"UTF-8\">" in html
        assert "<meta name=\"viewport\"" in html
        assert "role=\"presentation\"" in html
        assert "cellspacing=\"0\"" in html
        assert "cellpadding=\"0\"" in html
        
    def test_email_responsive_elements(self):
        """Test that the email includes responsive design elements."""
        mock_context = MagicMock()
        
        result = generate_html_email(
            tool_context=mock_context,
            subject_line="Responsive Test",
            headline="Responsive Email",
            body_copy="This should be responsive",
            cta_text="Click Me"
        )
        
        html = result["html_email"]
        
        # Check for responsive design elements
        assert "@media only screen and (max-width: 600px)" in html
        assert "mobile-padding" in html
        assert "max-width: 100%" in html
        
    def test_email_accessibility_features(self):
        """Test that the email includes accessibility features."""
        mock_context = MagicMock()
        
        result = generate_html_email(
            tool_context=mock_context,
            subject_line="Accessibility Test",
            headline="Accessible Email",
            body_copy="This email should be accessible",
            cta_text="Accessible CTA",
            logo_url="https://example.com/logo.png",
            brand_name="Accessible Brand"
        )
        
        html = result["html_email"]
        
        # Check for accessibility features
        assert "alt=\"Accessible Brand Logo\"" in html
        assert "role=\"presentation\"" in html
        # Check for proper heading structure
        assert "<h1" in html
