# AI Agency: Vertex Agents and Apps
A suite of orchestrated AI agents designed to automate and accelerate end-to-end content lifecycles through various AI models with expert humans in the loop throughout.

- Agents: Defined Agents for specified 
- App: (Coming Soon) A Frontend for Agent communication.

## Getting Started

### Create & Activate Virtual Environment:

```
# Create
python -m venv .venv
# Activate (each new terminal)
# macOS/Linux: source .venv/bin/activate
# Windows CMD: .venv\Scripts\activate.bat
# Windows PowerShell: .venv\Scripts\Activate.ps1
```

Note: Depending on your python installation you may need to use `python3` or `python3.12` for this command to work.

### Install ADK:

```
pip install google-adk
```

### Install gcloud cli

Instructions here: https://cloud.google.com/sdk/docs/install

### Authenticate to Google Cloud

```
gcloud auth login
```

### Create app/.env file

```
GOOGLE_GENAI_USE_VERTEXAI=TRUE
GOOGLE_CLOUD_PROJECT=agency-ai-automation
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_CLOUD_STORAGE=beeswax-knowledge
MODEL=gemini-2.5-flash-preview-05-20
ANALYSIS_MODEL=gemini-2.5-flash-preview-05-20
RESEARCH_MODEL=gemini-2.5-pro-preview-05-06
```

### Run the agents with ADK

```
cd agents
adk web
```

### Run the frontend UI

```
cd apps/client
npm install
npm run dev
```

### Run the backend API

```
cd apps/server
npm install
npm run dev
```

### Running Tests

For running tests and evaluation, install the extra dependencies:

```bash
poetry install --with dev
```

Then the tests and evaluation can be run from the `marketing_agency` directory using
the `pytest` module:

```bash
python3 -m pytest tests
python3 -m pytest eval
```