# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
# Usually ignored, but if you distribute binary builds, you might commit these.
# *.manifest
# *.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
# *.pot

# Django stuff:
# *.log  # Covered by generic *.log
# local_settings.py # Covered by generic local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Environments
.env*
.venv
env/
venv/
ENV/
env.bak
venv.bak

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sublime-workspace

# Log files
*.log

# Operating System Files
.DS_Store
Thumbs.db

# Secrets - BE VERY CAREFUL WITH THESE
secrets.json
config.json
local_settings.py
credentials.json
# Add any other files containing sensitive information

# Optional: If you handle media files that shouldn't be in the repo
# media/

node_modules/

# hide database from version control
apps/server/database/agency.db